# FOC API エンドポイント詳細仕様

## 1. masters（マスタデータ管理）API

### 1.1 日程項目管理

#### GET /api/masters/schedules/
**機能**: 日程項目一覧を取得
**認証**: IsAuthenticated
**パラメータ**: なし
**レスポンス**:
```json
[
  {
    "id": 1,
    "schedule_name": "通夜",
    "disp_order": 1,
    "created_at": "2024-01-01T10:00:00Z"
  }
]
```

### 1.2 施行スタイル管理

#### GET /api/masters/seko_styles/
**機能**: 施行スタイル一覧を取得
**認証**: IsAuthenticated
**パラメータ**: なし
**レスポンス**:
```json
[
  {
    "id": 1,
    "style_name": "家族葬",
    "description": "家族・親族のみで行う葬儀",
    "created_at": "2024-01-01T10:00:00Z"
  }
]
```

### 1.3 郵便番号検索

#### GET /api/masters/zipcode/
**機能**: 郵便番号から住所を検索
**認証**: IsAuthenticatedOrReadOnly
**パラメータ**:
- `zipcode` (string, required): 郵便番号（7桁）
**レスポンス**:
```json
{
  "zipcode": "1000001",
  "prefecture": "東京都",
  "city": "千代田区",
  "town": "千代田"
}
```

### 1.4 その他マスタAPI

#### GET /api/masters/wareki/
**機能**: 和暦一覧を取得

#### GET /api/masters/services/
**機能**: サービス一覧を取得

#### GET /api/masters/taxes/
**機能**: 税率一覧を取得

#### GET /api/masters/relationship/
**機能**: 関係性一覧を取得

#### GET /api/masters/henrei_params/
**機能**: 返礼品パラメータを取得

#### GET /api/masters/koden_params/
**機能**: 香典パラメータを取得

## 2. bases（拠点・組織管理）API

### 2.1 拠点基本操作

#### GET /api/bases/all/
**機能**: 全拠点の階層一覧を取得
**認証**: IsAuthenticated
**レスポンス**:
```json
[
  {
    "id": 1,
    "base_type": 0,
    "base_name": "システム管理",
    "company_code": "ADMIN",
    "children": [
      {
        "id": 2,
        "base_type": 1,
        "base_name": "○○葬儀社",
        "company_code": "COMP001",
        "children": []
      }
    ]
  }
]
```

#### POST /api/bases/
**機能**: 新規拠点を作成
**認証**: IsAuthenticated
**リクエスト**:
```json
{
  "base_type": 1,
  "parent": 1,
  "base_name": "新規葬儀社",
  "company_code": "COMP002",
  "zip_code": "1000001",
  "prefecture": "東京都",
  "address_1": "千代田区千代田1-1-1",
  "tel": "03-1234-5678"
}
```

#### GET /api/bases/<int:pk>/
**機能**: 拠点詳細情報を取得

#### PUT /api/bases/<int:pk>/
**機能**: 拠点情報を更新

#### DELETE /api/bases/<int:pk>/
**機能**: 拠点を論理削除

### 2.2 拠点関連情報

#### GET /api/bases/<int:base_id>/tokusho/
**機能**: 特定商取引法情報を取得・更新
**レスポンス**:
```json
{
  "company_name": "株式会社○○葬儀社",
  "representative": "代表取締役 山田太郎",
  "address": "東京都千代田区千代田1-1-1",
  "tel": "03-1234-5678",
  "business_license": "東京都知事許可第12345号"
}
```

#### GET /api/bases/<int:base_id>/focfee/
**機能**: FOC手数料設定を取得・更新

#### GET /api/bases/<int:base_id>/sms/
**機能**: SMS設定を取得・更新

## 3. staffs（スタッフ管理）API

### 3.1 認証関連

#### POST /api/staffs/login/
**機能**: スタッフログイン
**リクエスト**:
```json
{
  "username": "staff001",
  "password": "password123"
}
```
**レスポンス**:
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
  "staff": {
    "id": 1,
    "username": "staff001",
    "base": {
      "id": 2,
      "base_name": "○○葬儀社",
      "base_type": 1
    }
  }
}
```

#### POST /api/staffs/token/refresh/
**機能**: アクセストークンをリフレッシュ

#### GET /api/staffs/me/
**機能**: ログイン中スタッフの情報を取得

### 3.2 スタッフ管理

#### GET /api/staffs/
**機能**: スタッフ一覧を取得

#### POST /api/staffs/
**機能**: 新規スタッフを作成

#### GET /api/staffs/<int:pk>/
**機能**: スタッフ詳細を取得

#### PUT /api/staffs/<int:pk>/
**機能**: スタッフ情報を更新

#### DELETE /api/staffs/<int:pk>/
**機能**: スタッフを削除

## 4. seko（施行管理）API

### 4.1 施行基本操作

#### GET /api/seko/
**機能**: 施行一覧を取得
**認証**: IsAuthenticated
**パラメータ**:
- `base_id` (int, optional): 拠点ID
- `seko_date_from` (date, optional): 施行日From
- `seko_date_to` (date, optional): 施行日To
- `kojin_name` (string, optional): 故人名（部分一致）
**レスポンス**:
```json
{
  "count": 100,
  "next": "http://api/seko/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "seko_no": "2024-001",
      "seko_date": "2024-01-15",
      "base": {
        "id": 2,
        "base_name": "○○葬儀社"
      },
      "kojin": {
        "kojin_name": "山田太郎",
        "death_date": "2024-01-10"
      },
      "moshu": {
        "moshu_name": "山田花子",
        "tel": "090-1234-5678"
      }
    }
  ]
}
```

#### POST /api/seko/
**機能**: 新規施行を作成
**リクエスト**:
```json
{
  "base_id": 2,
  "seko_date": "2024-01-15",
  "seko_style_id": 1,
  "seko_place": "○○会館",
  "kojin": {
    "kojin_name": "山田太郎",
    "kojin_kana": "ヤマダタロウ",
    "birth_date": "1950-01-01",
    "death_date": "2024-01-10",
    "age": 74,
    "gender": 1
  },
  "moshu": {
    "moshu_name": "山田花子",
    "moshu_kana": "ヤマダハナコ",
    "tel": "090-1234-5678",
    "email": "<EMAIL>"
  }
}
```

#### GET /api/seko/<int:pk>/
**機能**: 施行詳細を取得

#### PUT /api/seko/<int:pk>/
**機能**: 施行情報を更新

### 4.2 施行関連情報

#### GET /api/seko/<int:seko_id>/kojin/
**機能**: 故人情報一覧を取得

#### POST /api/seko/<int:seko_id>/kojin/
**機能**: 故人情報を作成

#### GET /api/seko/<int:seko_id>/albums/
**機能**: 施行アルバム一覧を取得

#### POST /api/seko/<int:seko_id>/albums/
**機能**: 施行アルバムを作成

### 4.3 PDF・メール機能

#### GET /api/seko/<int:pk>/pdf/
**機能**: 不法書PDFを生成
**レスポンス**: PDF ファイル

#### POST /api/seko/<int:pk>/moshu_mail/
**機能**: 喪主案内メールを送信
**リクエスト**:
```json
{
  "subject": "施行案内",
  "message": "この度はお世話になります...",
  "send_immediately": true
}
```

### 4.4 認証・承認

#### POST /api/seko/login/
**機能**: 喪主ログイン
**リクエスト**:
```json
{
  "email": "<EMAIL>",
  "password": "moshu123"
}
```

#### POST /api/seko/<int:pk>/approve/
**機能**: 施行を承認

## 5. orders（注文管理）API

### 5.1 注文基本操作

#### GET /api/orders/
**機能**: 注文一覧を取得
**パラメータ**:
- `base_id` (int, optional): 拠点ID
- `entry_date_from` (date, optional): 注文日From
- `entry_date_to` (date, optional): 注文日To
- `status` (string, optional): 注文ステータス

#### POST /api/orders/
**機能**: 新規注文を作成
**リクエスト**:
```json
{
  "seko_id": 1,
  "base_id": 2,
  "customer_name": "田中一郎",
  "customer_email": "<EMAIL>",
  "details": [
    {
      "item_id": 1,
      "quantity": 2,
      "unit_price": 5000
    }
  ]
}
```

#### GET /api/orders/<int:pk>/
**機能**: 注文詳細を取得

#### POST /api/orders/<int:pk>/cancel/
**機能**: 注文をキャンセル

### 5.2 商品別注文管理

#### GET /api/orders/kumotsu/
**機能**: 供物注文一覧を取得

#### GET /api/orders/chobun/
**機能**: 弔文注文一覧を取得

#### GET /api/orders/koden/
**機能**: 香典一覧を取得

### 5.3 決済関連

#### POST /api/orders/epsilon/return/
**機能**: Epsilon決済結果を受信

#### GET /api/orders/paymentresult/
**機能**: 決済結果一覧を取得

## 6. henrei（返礼品管理）API

### 6.1 返礼品基本操作

#### POST /api/henrei/add/
**機能**: 返礼品注文を作成
**リクエスト**:
```json
{
  "seko_id": 1,
  "henrei_type": 1,
  "quantity": 100,
  "unit_price": 500,
  "delivery_date": "2024-01-20",
  "delivery_address": "東京都千代田区千代田1-1-1"
}
```

#### GET /api/henrei/list/
**機能**: 返礼品一覧を取得

#### GET /api/henrei/orders/
**機能**: 返礼品注文一覧を取得

#### GET /api/henrei/orders/<int:pk>/
**機能**: 返礼品注文詳細を取得・更新

#### GET /api/henrei/orders/<int:pk>/pdf/
**機能**: 返礼品注文書PDFを生成

#### POST /api/henrei/orders/order_status/
**機能**: 返礼品注文ステータスを更新

### 6.2 関連商品管理

#### GET /api/henrei/koden/
**機能**: 返礼品香典一覧を取得

#### GET /api/henrei/kumotsu/
**機能**: 返礼品供物一覧を取得

## 7. hoyo（法要管理）API

### 7.1 法要基本操作

#### GET /api/hoyo/
**機能**: 法要一覧を取得
**パラメータ**:
- `base_id` (int, optional): 拠点ID
- `hoyo_date_from` (date, optional): 法要日From
- `hoyo_date_to` (date, optional): 法要日To
- `hoyo_style_id` (int, optional): 法要スタイルID

#### POST /api/hoyo/
**機能**: 新規法要を作成
**リクエスト**:
```json
{
  "base_id": 2,
  "hoyo_name": "一周忌法要",
  "hoyo_date": "2025-01-10",
  "hoyo_style_id": 1,
  "seko_id": 1,
  "participant_count": 20,
  "venue": "○○寺",
  "contact_person": "山田花子",
  "contact_tel": "090-1234-5678"
}
```

#### GET /api/hoyo/<int:pk>/
**機能**: 法要詳細を取得

#### PUT /api/hoyo/<int:pk>/
**機能**: 法要情報を更新

### 7.2 法要サンプル管理

#### GET /api/hoyo/samples/
**機能**: 法要サンプル一覧を取得

#### POST /api/hoyo/samples/
**機能**: 法要サンプルを作成

### 7.3 法要メールテンプレート

#### GET /api/hoyo/mail_templates/
**機能**: 法要メールテンプレート一覧を取得

#### POST /api/hoyo/mail_templates/
**機能**: 法要メールテンプレートを作成

### 7.4 法要施行管理

#### GET /api/hoyo/seko/
**機能**: 法要施行一覧を取得

#### GET /api/hoyo/seko/<int:pk>/pdf/
**機能**: 法要PDFを生成

## 8. その他サポートAPI

### 8.1 items（商品管理）

#### GET /api/items/
**機能**: 商品一覧を取得
**パラメータ**:
- `category` (string, optional): 商品カテゴリ
- `base_id` (int, optional): 拠点ID
- `search` (string, optional): 商品名検索

#### POST /api/items/
**機能**: 新規商品を作成

#### POST /api/items/bulk_upsert/
**機能**: 商品を一括更新
**リクエスト**:
```json
{
  "items": [
    {
      "item_code": "ITEM001",
      "item_name": "白菊花束",
      "category": "供花",
      "price": 5000,
      "tax_rate": 10
    }
  ]
}
```

### 8.2 suppliers（仕入先管理）

#### GET /api/suppliers/
**機能**: 仕入先一覧を取得

#### POST /api/suppliers/
**機能**: 新規仕入先を作成

### 8.3 invoices（請求管理）

#### GET /api/invoices/sales/
**機能**: 売上一覧を取得

#### POST /api/invoices/sales/<int:pk>/fix/
**機能**: 売上を確定

#### GET /api/invoices/<int:pk>/pdf/
**機能**: 請求書PDFを生成

### 8.4 after_follow（アフターフォロー）

#### GET /api/after_follow/groups/
**機能**: アフターフォローグループ一覧を取得

#### GET /api/after_follow/sekoaf/
**機能**: 施行アフターフォロー一覧を取得

### 8.5 その他

#### POST /api/inquiry/
**機能**: 問い合わせメールを送信

#### GET /api/faqs/
**機能**: FAQ一覧を取得

#### GET /api/advertises/
**機能**: 広告一覧を取得

## 共通仕様

### 認証方式
- **JWT認証**: Bearer トークンをAuthorizationヘッダーに設定
- **トークン有効期限**: アクセストークン1時間、リフレッシュトークン7日

### エラーレスポンス
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "入力データに誤りがあります",
    "details": {
      "field_name": ["エラーメッセージ"]
    }
  }
}
```

### ページネーション
```json
{
  "count": 100,
  "next": "http://api/endpoint/?page=2",
  "previous": null,
  "results": []
}
```

### 日時フォーマット
- **日付**: YYYY-MM-DD
- **日時**: YYYY-MM-DDTHH:MM:SSZ (ISO 8601)

### ファイルアップロード
- **対応形式**: JPG, PNG, GIF, PDF, MP4
- **最大サイズ**: 10MB
- **レスポンス**: ファイルURL
