# FOCプロジェクト データフロー図

## システム全体データフロー（詳細版）

### 1. フロントエンド層

```mermaid
graph TB
    subgraph "フロントエンド (Angular 10)"
        subgraph "Company（葬儀社・システム会社）"
            A1[認証・基本機能<br/>login, top, frame]
            A2[施行管理<br/>seko, seko-edit, seko-af]
            A3[マスタ管理<br/>base, staff, supplier, item-service]
            A4[業務管理<br/>henreihin, chobun, kumotsu, koden]
            A5[集計・帳票<br/>monthly-summary, sales-summary, invoice]
            A6[法要管理<br/>hoyo-master, hoyo-sample, hoyo-mail-template]
            A7[システム管理<br/>fuho-sample, chobun-daishi, advertise]
            A8[アフターフォロー<br/>af-group, af-contact, event-guidance]
        end

        subgraph "Customer（顧客）"
            B1[申込・注文<br/>huho, chobun1/2, kumotsu, koden1/2]
            B2[カート・決済<br/>cart, order-entry, order-confirm, order-payment]
            B3[サポート<br/>inquiry, hoyo, receipt]
            B4[規約・ポリシー<br/>privacy-policy, tokushoho, userpolicy]
        end

        subgraph "Family（遺族）"
            C1[認証<br/>soke-approve, login, change-password]
            C2[施行情報<br/>seko, homei, henrei, info]
            C3[メモリアル<br/>memorial, image-share]
            C4[サポート<br/>hoyo, inquiry, faq, soke-af]
        end
    end

    A1 --> D[認証システム]
    A2 --> D
    A3 --> D
    A4 --> D
    A5 --> D
    A6 --> D
    A7 --> D
    A8 --> D
    B1 --> D
    B2 --> D
    B3 --> D
    B4 --> D
    C1 --> D
    C2 --> D
    C3 --> D
    C4 --> D
```

### 2. 認証・権限管理層

```mermaid
graph TB
    subgraph "認証システム (JWT)"
        D1[スタッフ認証<br/>AuthFGuard]
        D2[遺族認証<br/>AuthSGuard]
        D3[管理者権限<br/>AdminGuard]
        D4[スタッフ専用<br/>StaffOnlyGuard]
        D5[注文権限<br/>OrderGuard]
    end

    subgraph "権限レベル"
        P1[ADMIN<br/>システム管理者]
        P2[COMPANY<br/>葬儀社管理者]
        P3[DEPARTMENT<br/>部門管理者]
        P4[HALL<br/>式場スタッフ]
        P5[OTHER<br/>その他]
    end

    D1 --> P1
    D1 --> P2
    D1 --> P3
    D1 --> P4
    D3 --> P1
    D3 --> P2
    D4 --> P3
    D4 --> P4

    P1 --> E[API Gateway]
    P2 --> E
    P3 --> E
    P4 --> E
    P5 --> E
```

### 3. バックエンドAPI層（コアモジュール）

#### 3-1. masters（マスタ管理）

```mermaid
graph TB
    subgraph "masters API エンドポイント"
        F1[GET /masters/schedules/<br/>日程項目一覧取得]
        F2[GET /masters/seko_styles/<br/>施行スタイル一覧取得]
        F3[GET /masters/zipcode/<br/>郵便番号検索]
        F4[GET /masters/wareki/<br/>和暦一覧取得]
        F5[GET /masters/fuho_sample/<br/>不法サンプルマスタ一覧]
        F6[GET /masters/chobun_daishi/<br/>弔文代師マスタ一覧]
        F7[GET /masters/chobun_sample/<br/>弔文サンプル一覧]
        F8[GET /masters/services/<br/>サービス一覧取得]
        F9[GET /masters/taxes/<br/>税率一覧取得]
        F10[GET /masters/relationship/<br/>関係性一覧取得]
    end

    subgraph "masters API エンドポイント（続き）"
        F11[GET /masters/henrei_params/<br/>返礼品パラメータ取得]
        F12[GET /masters/koden_params/<br/>香典パラメータ取得]
        F13[GET /masters/hoyo_style/<br/>法要スタイル一覧]
        F14[GET /masters/hoyo_default/<br/>法要デフォルトマスタ一覧]
        F15[GET /masters/hoyo_sample/<br/>法要サンプルマスタ一覧]
        F16[GET /masters/soke_menu/<br/>遺族メニュー一覧]
    end

    F1 --> DB[PostgreSQL<br/>マスタデータ]
    F2 --> DB
    F3 --> DB
    F4 --> DB
    F5 --> DB
    F6 --> DB
    F7 --> DB
    F8 --> DB
    F9 --> DB
    F10 --> DB
    F11 --> DB
    F12 --> DB
    F13 --> DB
    F14 --> DB
    F15 --> DB
    F16 --> DB
```

#### 3-2. bases（拠点管理）

```mermaid
graph TB
    subgraph "bases API エンドポイント（基本操作）"
        G1[GET /bases/<br/>拠点作成フォーム]
        G2[POST /bases/<br/>拠点作成]
        G3[GET /bases/&lt;pk&gt;/<br/>拠点詳細取得]
        G4[PUT /bases/&lt;pk&gt;/<br/>拠点情報更新]
        G5[PATCH /bases/&lt;pk&gt;/<br/>拠点情報部分更新]
        G6[DELETE /bases/&lt;pk&gt;/<br/>拠点情報削除]
        G7[GET /bases/all/<br/>全拠点階層一覧取得]
    end

    subgraph "bases API エンドポイント（関連情報）"
        G8[GET /bases/&lt;base_id&gt;/focfee/<br/>FOC手数料取得]
        G9[PUT /bases/&lt;base_id&gt;/focfee/<br/>FOC手数料更新]
        G10[GET /bases/&lt;base_id&gt;/tokusho/<br/>特商法情報取得]
        G11[PUT /bases/&lt;base_id&gt;/tokusho/<br/>特商法情報更新]
        G12[GET /bases/&lt;base_id&gt;/sms/<br/>SMS設定取得]
        G13[PUT /bases/&lt;base_id&gt;/sms/<br/>SMS設定更新]
        G14[POST /bases/&lt;pk&gt;/inquiry/<br/>問い合わせメール送信]
    end

    G1 --> DB[PostgreSQL<br/>拠点データ]
    G2 --> DB
    G3 --> DB
    G4 --> DB
    G5 --> DB
    G6 --> DB
    G7 --> DB
    G8 --> DB
    G9 --> DB
    G10 --> DB
    G11 --> DB
    G12 --> DB
    G13 --> DB
    G14 --> MAIL[SendGrid<br/>メール送信]
```

#### 3-3. staffs（スタッフ管理）

```mermaid
graph TB
    subgraph "staffs API エンドポイント（認証）"
        S1[POST /staffs/login/<br/>スタッフログイン]
        S2[POST /staffs/token/refresh/<br/>トークンリフレッシュ]
        S3[POST /staffs/token/verify/<br/>トークン検証]
        S4[GET /staffs/me/<br/>自分の情報取得]
    end

    subgraph "staffs API エンドポイント（管理）"
        S5[GET /staffs/lower/&lt;base_id&gt;/<br/>下位拠点スタッフ一覧]
        S6[GET /staffs/<br/>スタッフ一覧]
        S7[POST /staffs/<br/>スタッフ作成]
        S8[GET /staffs/&lt;pk&gt;/<br/>スタッフ詳細取得]
        S9[PUT /staffs/&lt;pk&gt;/<br/>スタッフ情報更新]
        S10[PATCH /staffs/&lt;pk&gt;/<br/>スタッフ情報部分更新]
        S11[DELETE /staffs/&lt;pk&gt;/<br/>スタッフ削除]
    end

    S1 --> JWT[JWT認証<br/>トークン発行]
    S2 --> JWT
    S3 --> JWT
    S4 --> DB[PostgreSQL<br/>スタッフデータ]
    S5 --> DB
    S6 --> DB
    S7 --> DB
    S8 --> DB
    S9 --> DB
    S10 --> DB
    S11 --> DB
```

### 4. バックエンドAPI層（業務モジュール）

#### 4-1. seko（施行管理）- 基本操作

```mermaid
graph TB
    subgraph "seko API エンドポイント（基本CRUD）"
        H1[GET /seko/<br/>施行一覧取得]
        H2[POST /seko/<br/>施行作成]
        H3[GET /seko/&lt;pk&gt;/<br/>施行詳細取得]
        H4[PUT /seko/&lt;pk&gt;/<br/>施行情報更新]
        H5[PATCH /seko/&lt;pk&gt;/<br/>施行情報部分更新]
        H6[DELETE /seko/&lt;pk&gt;/<br/>施行削除]
        H7[GET /seko/&lt;pk&gt;/fuho/<br/>施行不法詳細]
        H8[GET /seko/list/<br/>ナウエル施行一覧]
    end

    subgraph "seko API エンドポイント（関連情報）"
        H9[GET /seko/&lt;seko_id&gt;/kojin/<br/>故人情報一覧]
        H10[POST /seko/&lt;seko_id&gt;/kojin/<br/>故人情報作成]
        H11[GET /seko/&lt;seko_id&gt;/kojin/&lt;pk&gt;/<br/>故人詳細・更新・削除]
        H12[GET /seko/&lt;seko_id&gt;/albums/<br/>アルバム一覧]
        H13[POST /seko/&lt;seko_id&gt;/albums/<br/>アルバム作成]
        H14[GET /seko/&lt;seko_id&gt;/albums/&lt;pk&gt;/<br/>アルバム詳細・更新・削除]
        H15[GET /seko/&lt;seko_id&gt;/share_images/<br/>共有画像一覧]
        H16[POST /seko/&lt;seko_id&gt;/share_images/<br/>共有画像作成]
    end

    H1 --> DB[PostgreSQL<br/>施行データ]
    H2 --> DB
    H3 --> DB
    H4 --> DB
    H5 --> DB
    H6 --> DB
    H7 --> DB
    H8 --> DB
    H9 --> DB
    H10 --> DB
    H11 --> DB
    H12 --> DB
    H13 --> DB
    H14 --> DB
    H15 --> DB
    H16 --> DB
```

#### 4-2. seko（施行管理）- 特殊機能

```mermaid
graph TB
    subgraph "seko API エンドポイント（PDF・メール）"
        H17[GET /seko/&lt;pk&gt;/pdf/<br/>不法書PDF生成]
        H18[POST /seko/&lt;pk&gt;/moshu_mail/<br/>喪主案内メール送信]
        H19[GET /seko/&lt;pk&gt;/qrcode/<br/>QRコードPDF生成]
        H20[GET /seko/&lt;pk&gt;/homeicho/chobun_pdf/<br/>芳名帳弔文PDF]
        H21[GET /seko/&lt;pk&gt;/homeicho/kumotsu_pdf/<br/>芳名帳供物PDF]
        H22[GET /seko/&lt;pk&gt;/homeicho/koden_pdf/<br/>芳名帳香典PDF]
        H23[GET /seko/&lt;pk&gt;/homeicho/msg_pdf/<br/>芳名帳メッセージPDF]
        H24[GET /seko/&lt;pk&gt;/msg_pdf/<br/>メッセージPDF]
    end

    subgraph "seko API エンドポイント（認証・承認）"
        H25[POST /seko/login/<br/>喪主ログイン]
        H26[POST /seko/token/refresh/<br/>喪主トークンリフレッシュ]
        H27[POST /seko/token/verify/<br/>喪主トークン検証]
        H28[POST /seko/&lt;pk&gt;/approve/<br/>施行承認]
        H29[POST /seko/soke/&lt;pk&gt;/approve/<br/>遺族承認]
        H30[GET /seko/moshu/<br/>喪主一覧]
        H31[GET /seko/moshu/&lt;pk&gt;/<br/>喪主詳細・更新]
    end

    H17 --> PDF[PDF生成<br/>WeasyPrint]
    H18 --> MAIL[SendGrid<br/>メール送信]
    H19 --> PDF
    H20 --> PDF
    H21 --> PDF
    H22 --> PDF
    H23 --> PDF
    H24 --> PDF
    H25 --> JWT[JWT認証<br/>喪主トークン]
    H26 --> JWT
    H27 --> JWT
    H28 --> DB[PostgreSQL<br/>承認データ]
    H29 --> DB
    H30 --> DB
    H31 --> DB
```

#### 4-3. orders（注文管理）

```mermaid
graph TB
    subgraph "orders API エンドポイント（基本操作）"
        I1[GET /orders/<br/>注文一覧取得]
        I2[POST /orders/<br/>注文作成]
        I3[GET /orders/&lt;pk&gt;/<br/>注文詳細取得]
        I4[PUT /orders/&lt;pk&gt;/<br/>注文更新]
        I5[GET /orders/&lt;pk&gt;/receipt/<br/>領収書PDF生成]
        I6[POST /orders/&lt;pk&gt;/cancel/<br/>注文キャンセル]
        I7[GET /orders/fdn/<br/>FDN注文一覧]
        I8[GET /orders/paymentresult/<br/>決済結果一覧]
    end

    subgraph "orders API エンドポイント（商品別）"
        I9[GET /orders/kumotsu/<br/>供物一覧取得]
        I10[GET /orders/kumotsu/&lt;pk&gt;/<br/>供物詳細・更新]
        I11[POST /orders/kumotsu/order_status/<br/>供物注文ステータス更新]
        I12[GET /orders/kumotsu/&lt;pk&gt;/pdf/<br/>供物PDF生成]
        I13[GET /orders/kumotsu/&lt;pk&gt;/fax/<br/>供物FAX送信]
        I14[GET /orders/chobun/<br/>弔文一覧取得]
        I15[GET /orders/chobun/&lt;pk&gt;/<br/>弔文詳細・更新]
        I16[GET /orders/koden/<br/>香典一覧取得]
    end

    I1 --> DB[PostgreSQL<br/>注文データ]
    I2 --> DB
    I3 --> DB
    I4 --> DB
    I5 --> PDF[PDF生成<br/>領収書]
    I6 --> DB
    I7 --> DB
    I8 --> PAY[Epsilon決済<br/>結果取得]
    I9 --> DB
    I10 --> DB
    I11 --> DB
    I12 --> PDF
    I13 --> FAX[FAX送信<br/>システム]
    I14 --> DB
    I15 --> DB
    I16 --> DB
```

#### 4-4. henrei（返礼品管理）

```mermaid
graph TB
    subgraph "henrei API エンドポイント（基本操作）"
        J1[POST /henrei/add/<br/>返礼品注文作成]
        J2[GET /henrei/list/<br/>返礼品一覧取得]
        J3[GET /henrei/orders/<br/>返礼品注文一覧取得]
        J4[GET /henrei/orders/&lt;pk&gt;/<br/>返礼品注文詳細・更新]
        J5[GET /henrei/orders/&lt;pk&gt;/pdf/<br/>返礼品注文PDF生成]
        J6[GET /henrei/orders/&lt;pk&gt;/fax/<br/>返礼品注文FAX送信]
        J7[POST /henrei/orders/order_status/<br/>返礼品注文ステータス更新]
        J8[POST /henrei/place/<br/>返礼品注文確定]
    end

    subgraph "henrei API エンドポイント（関連商品）"
        J9[GET /henrei/koden/<br/>返礼品香典一覧取得]
        J10[GET /henrei/koden/&lt;pk&gt;/<br/>返礼品香典詳細・更新]
        J11[GET /henrei/kumotsu/<br/>返礼品供物一覧取得]
        J12[GET /henrei/kumotsu/&lt;pk&gt;/<br/>返礼品供物詳細・更新]
    end

    J1 --> DB[PostgreSQL<br/>返礼品データ]
    J2 --> DB
    J3 --> DB
    J4 --> DB
    J5 --> PDF[PDF生成<br/>返礼品注文書]
    J6 --> FAX[FAX送信<br/>システム]
    J7 --> DB
    J8 --> DB
    J9 --> DB
    J10 --> DB
    J11 --> DB
    J12 --> DB
```

### 5. バックエンドAPI層（サポートモジュール）

#### 5-1. hoyo（法要管理）

```mermaid
graph TB
    subgraph "hoyo API エンドポイント（基本操作）"
        K1[GET /hoyo/<br/>法要一覧取得]
        K2[POST /hoyo/<br/>法要作成]
        K3[GET /hoyo/&lt;pk&gt;/<br/>法要詳細取得・更新・削除]
        K4[PUT /hoyo/&lt;pk&gt;/<br/>法要更新]
        K5[DELETE /hoyo/&lt;pk&gt;/<br/>法要削除]
        K6[GET /hoyo/mails/<br/>法要メール一覧取得]
    end

    subgraph "hoyo API エンドポイント（サンプル・テンプレート）"
        K7[GET /hoyo/samples/<br/>法要サンプル一覧取得]
        K8[POST /hoyo/samples/<br/>法要サンプル作成]
        K9[GET /hoyo/samples/&lt;pk&gt;/<br/>法要サンプル詳細・更新・削除]
        K10[GET /hoyo/mail_templates/<br/>法要メールテンプレート一覧]
        K11[POST /hoyo/mail_templates/<br/>法要メールテンプレート作成]
        K12[GET /hoyo/mail_templates/&lt;pk&gt;/<br/>法要メールテンプレート詳細・更新・削除]
    end

    subgraph "hoyo API エンドポイント（施行関連）"
        K13[GET /hoyo/seko/<br/>法要施行一覧取得]
        K14[POST /hoyo/seko/<br/>法要施行作成]
        K15[GET /hoyo/seko/&lt;pk&gt;/<br/>法要施行詳細・更新・削除]
        K16[GET /hoyo/seko/&lt;pk&gt;/pdf/<br/>法要PDF生成]
    end

    K1 --> DB[PostgreSQL<br/>法要データ]
    K2 --> DB
    K3 --> DB
    K4 --> DB
    K5 --> DB
    K6 --> MAIL[SendGrid<br/>法要メール]
    K7 --> DB
    K8 --> DB
    K9 --> DB
    K10 --> DB
    K11 --> DB
    K12 --> DB
    K13 --> DB
    K14 --> DB
    K15 --> DB
    K16 --> PDF[PDF生成<br/>法要書類]
```

#### 5-2. items・suppliers（商品・仕入先管理）

```mermaid
graph TB
    subgraph "items API エンドポイント"
        L1[GET /items/<br/>商品一覧取得]
        L2[POST /items/<br/>商品作成]
        L3[GET /items/&lt;pk&gt;/<br/>商品詳細取得・更新・削除]
        L4[PUT /items/&lt;pk&gt;/<br/>商品更新]
        L5[DELETE /items/&lt;pk&gt;/<br/>商品削除]
        L6[POST /items/bulk_upsert/<br/>商品一括更新]
        L7[POST /items/base/&lt;base_id&gt;/<br/>商品画像インポート]
    end

    subgraph "suppliers API エンドポイント"
        L8[GET /suppliers/<br/>仕入先一覧取得]
        L9[POST /suppliers/<br/>仕入先作成]
        L10[GET /suppliers/&lt;pk&gt;/<br/>仕入先詳細取得・更新・削除]
        L11[PUT /suppliers/&lt;pk&gt;/<br/>仕入先更新]
        L12[DELETE /suppliers/&lt;pk&gt;/<br/>仕入先削除]
    end

    L1 --> DB[PostgreSQL<br/>商品データ]
    L2 --> DB
    L3 --> DB
    L4 --> DB
    L5 --> DB
    L6 --> DB
    L7 --> STORAGE[ファイルストレージ<br/>商品画像]
    L8 --> DB
    L9 --> DB
    L10 --> DB
    L11 --> DB
    L12 --> DB
```

#### 5-3. invoices・after_follow（請求・アフターフォロー）

```mermaid
graph TB
    subgraph "invoices API エンドポイント"
        M1[GET /invoices/sales/<br/>売上一覧取得]
        M2[POST /invoices/sales/&lt;pk&gt;/fix/<br/>売上確定]
        M3[POST /invoices/sales/&lt;pk&gt;/fix/cancel/<br/>売上確定キャンセル]
        M4[POST /invoices/gather_sales/<br/>売上集計]
        M5[GET /invoices/&lt;pk&gt;/pdf/<br/>請求書PDF生成]
    end

    subgraph "after_follow API エンドポイント（グループ）"
        M6[GET /after_follow/groups/<br/>AFグループ一覧取得]
        M7[POST /after_follow/groups/<br/>AFグループ作成]
        M8[GET /after_follow/groups/all/<br/>全AFグループ一覧]
        M9[GET /after_follow/groups/&lt;pk&gt;/<br/>AFグループ詳細・更新・削除]
        M10[GET /after_follow/contact_ways/<br/>AF連絡方法一覧]
        M11[GET /after_follow/contact_ways/&lt;pk&gt;/<br/>AF連絡方法詳細・更新・削除]
    end

    subgraph "after_follow API エンドポイント（活動）"
        M12[GET /after_follow/<br/>アフターフォロー一覧取得]
        M13[POST /after_follow/<br/>アフターフォロー作成]
        M14[GET /after_follow/&lt;pk&gt;/<br/>アフターフォロー詳細・更新・削除]
        M15[GET /after_follow/sekoaf/<br/>施行AF一覧取得]
        M16[GET /after_follow/sekoaf/&lt;pk&gt;/<br/>施行AF詳細・更新・削除]
        M17[GET /after_follow/activity/<br/>AF活動一覧取得]
        M18[GET /after_follow/activity/&lt;pk&gt;/<br/>AF活動詳細・更新・削除]
    end

    M1 --> DB[PostgreSQL<br/>売上データ]
    M2 --> DB
    M3 --> DB
    M4 --> DB
    M5 --> PDF[PDF生成<br/>請求書]
    M6 --> DB
    M7 --> DB
    M8 --> DB
    M9 --> DB
    M10 --> DB
    M11 --> DB
    M12 --> DB
    M13 --> DB
    M14 --> DB
    M15 --> DB
    M16 --> DB
    M17 --> DB
    M18 --> DB
```

#### 5-4. その他サポートモジュール

```mermaid
graph TB
    subgraph "その他 API エンドポイント"
        N1[POST /inquiry/<br/>問い合わせメール送信]
        N2[GET /event_mails/<br/>イベントメール一覧取得]
        N3[GET /faqs/<br/>FAQ一覧取得・作成]
        N4[GET /faqs/&lt;pk&gt;/<br/>FAQ詳細・更新・削除]
        N5[GET /advertises/<br/>広告一覧取得・作成]
        N6[GET /advertises/&lt;pk&gt;/<br/>広告詳細・更新・削除]
        N7[GET /fuho_samples/<br/>不法サンプル一覧取得・作成]
        N8[GET /fuho_samples/&lt;pk&gt;/<br/>不法サンプル詳細・更新・削除]
    end

    subgraph "特殊 API エンドポイント"
        N9[GET /chobun_daishi/<br/>弔文台紙一覧取得]
        N10[GET /service_reception_terms/<br/>サービス受付条件一覧取得]
    end

    N1 --> MAIL[SendGrid<br/>問い合わせメール]
    N2 --> MAIL
    N3 --> DB[PostgreSQL<br/>FAQ・広告データ]
    N4 --> DB
    N5 --> DB
    N6 --> DB
    N7 --> DB
    N8 --> DB
    N9 --> DB
    N10 --> DB
```

## 主要業務フロー

### 1. 施行管理フロー

```mermaid
sequenceDiagram
    participant CS as 葬儀社スタッフ
    participant API as Django API
    participant DB as PostgreSQL
    participant FM as 遺族
    
    CS->>API: 施行情報登録
    API->>DB: seko, kojin, moshu作成
    DB-->>API: 施行ID返却
    API-->>CS: 登録完了
    
    CS->>API: 遺族認証情報設定
    API->>DB: moshu認証情報更新
    
    FM->>API: 遺族ログイン
    API->>DB: 認証情報確認
    DB-->>API: 認証成功
    API-->>FM: JWT発行
    
    FM->>API: 施行情報閲覧
    API->>DB: 施行情報取得
    DB-->>API: 施行データ
    API-->>FM: 施行情報表示
```

### 2. 注文管理フロー

```mermaid
sequenceDiagram
    participant CU as 顧客
    participant API as Django API
    participant DB as PostgreSQL
    participant EP as Epsilon決済
    participant CS as 葬儀社
    
    CU->>API: 商品選択・カート追加
    API->>DB: カート情報保存
    
    CU->>API: 注文確定
    API->>DB: Entry, EntryDetail作成
    
    CU->>API: 決済処理
    API->>EP: 決済要求
    EP-->>API: 決済結果
    API->>DB: PaymentResult保存
    API-->>CU: 注文完了
    
    API->>CS: 注文通知メール
    CS->>API: 注文確認・処理
```

### 3. 法要管理フロー

```mermaid
sequenceDiagram
    participant CU as 顧客
    participant API as Django API
    participant DB as PostgreSQL
    participant MAIL as SendGrid
    participant CS as 葬儀社
    
    CU->>API: 法要申込
    API->>DB: Hoyo, HoyoSeko作成
    
    CS->>API: 法要承認
    API->>DB: 法要ステータス更新
    
    API->>DB: メールテンプレート取得
    API->>MAIL: 法要案内メール送信
    MAIL-->>CU: 法要案内受信
    
    API->>DB: HoyoSchedule作成
    CS->>API: 法要実施報告
```

## モジュール間依存関係

```mermaid
graph TD
    A[masters] --> B[bases]
    A --> C[seko]
    A --> D[hoyo]
    A --> E[orders]
    
    B --> C
    B --> F[staffs]
    
    C --> G[henrei]
    C --> D
    C --> H[after_follow]
    C --> I[inquiry]
    
    E --> G
    E --> J[invoices]
    
    D --> K[event_mails]
    H --> K
    
    L[suppliers] --> E
    M[items] --> E
    M --> C
    
    N[faqs] --> O[advertises]
```

## データベース関係図

```mermaid
erDiagram
    Base ||--o{ Seko : "拠点-施行"
    Base ||--o{ Staff : "拠点-スタッフ"
    Base {
        int id PK
        int base_type
        int parent_id FK
        string base_name
        string company_code
    }
    
    Seko ||--|| Kojin : "施行-故人"
    Seko ||--|| Moshu : "施行-喪主"
    Seko ||--o{ SekoSchedule : "施行-スケジュール"
    Seko ||--o{ SekoItem : "施行-商品"
    Seko ||--o{ SekoService : "施行-サービス"
    Seko {
        int id PK
        int base_id FK
        string seko_no
        date seko_date
        int seko_style_id FK
    }
    
    Kojin {
        int id PK
        int seko_id FK
        string kojin_name
        date birth_date
        date death_date
    }
    
    Moshu {
        int id PK
        int seko_id FK
        string moshu_name
        string tel
        string email
        string password
    }
    
    Entry ||--o{ EntryDetail : "注文-詳細"
    Entry {
        int id PK
        int seko_id FK
        int base_id FK
        datetime entry_datetime
        int total_amount
    }
    
    EntryDetail {
        int id PK
        int entry_id FK
        int item_id FK
        int quantity
        int unit_price
    }
    
    Hoyo ||--|| HoyoSeko : "法要-施行"
    Hoyo {
        int id PK
        int base_id FK
        string hoyo_name
        date hoyo_date
        int hoyo_style_id FK
    }
    
    HoyoSeko {
        int id PK
        int hoyo_id FK
        int seko_id FK
    }
```

## 完全API エンドポイント一覧

### 1. masters（マスタデータ管理）
```
GET    /api/masters/schedules/              # 日程項目一覧取得
GET    /api/masters/seko_styles/            # 施行スタイル一覧取得
GET    /api/masters/zipcode/                # 郵便番号検索
GET    /api/masters/wareki/                 # 和暦一覧取得
GET    /api/masters/fuho_sample/            # 不法サンプルマスタ一覧
GET    /api/masters/chobun_daishi/          # 弔文代師マスタ一覧
GET    /api/masters/chobun_sample/          # 弔文サンプル一覧
GET    /api/masters/services/              # サービス一覧取得
GET    /api/masters/taxes/                 # 税率一覧取得
GET    /api/masters/relationship/          # 関係性一覧取得
GET    /api/masters/henrei_params/         # 返礼品パラメータ取得
GET    /api/masters/koden_params/          # 香典パラメータ取得
GET    /api/masters/hoyo_style/            # 法要スタイル一覧
GET    /api/masters/hoyo_default/          # 法要デフォルトマスタ一覧
GET    /api/masters/hoyo_sample/           # 法要サンプルマスタ一覧
GET    /api/masters/soke_menu/             # 遺族メニュー一覧
```

### 2. bases（拠点・組織管理）
```
GET    /api/bases/                         # 拠点作成
POST   /api/bases/                         # 拠点作成
GET    /api/bases/<int:pk>/                # 拠点詳細取得
PUT    /api/bases/<int:pk>/                # 拠点情報更新
PATCH  /api/bases/<int:pk>/                # 拠点情報部分更新
DELETE /api/bases/<int:pk>/                # 拠点情報削除
GET    /api/bases/<int:base_id>/focfee/    # FOC手数料取得・更新
PUT    /api/bases/<int:base_id>/focfee/    # FOC手数料更新
GET    /api/bases/<int:base_id>/tokusho/   # 特商法情報取得・更新
PUT    /api/bases/<int:base_id>/tokusho/   # 特商法情報更新
GET    /api/bases/<int:base_id>/sms/       # SMS設定取得・更新
PUT    /api/bases/<int:base_id>/sms/       # SMS設定更新
POST   /api/bases/<int:pk>/inquiry/        # 問い合わせメール送信
GET    /api/bases/all/                     # 全拠点階層一覧取得
```

### 3. staffs（スタッフ管理）
```
POST   /api/staffs/login/                  # スタッフログイン
POST   /api/staffs/token/refresh/          # トークンリフレッシュ
POST   /api/staffs/token/verify/           # トークン検証
GET    /api/staffs/me/                     # 自分の情報取得
GET    /api/staffs/lower/<int:base_id>/    # 下位拠点スタッフ一覧
GET    /api/staffs/                        # スタッフ一覧
POST   /api/staffs/                        # スタッフ作成
GET    /api/staffs/<int:pk>/               # スタッフ詳細取得
PUT    /api/staffs/<int:pk>/               # スタッフ情報更新
PATCH  /api/staffs/<int:pk>/               # スタッフ情報部分更新
DELETE /api/staffs/<int:pk>/               # スタッフ削除
```

### 4. seko（施行管理）
```
GET    /api/seko/                          # 施行一覧取得
POST   /api/seko/                          # 施行作成
GET    /api/seko/<int:pk>/                 # 施行詳細取得
PUT    /api/seko/<int:pk>/                 # 施行情報更新
PATCH  /api/seko/<int:pk>/                 # 施行情報部分更新
DELETE /api/seko/<int:pk>/                 # 施行削除
GET    /api/seko/<int:pk>/fuho/            # 施行不法詳細
GET    /api/seko/<int:seko_id>/kojin/      # 故人情報一覧・作成
POST   /api/seko/<int:seko_id>/kojin/      # 故人情報作成
GET    /api/seko/<int:seko_id>/kojin/<int:pk>/  # 故人詳細・更新・削除
PUT    /api/seko/<int:seko_id>/kojin/<int:pk>/  # 故人情報更新
DELETE /api/seko/<int:seko_id>/kojin/<int:pk>/  # 故人削除
GET    /api/seko/<int:seko_id>/albums/     # アルバム一覧・作成
POST   /api/seko/<int:seko_id>/albums/     # アルバム作成
GET    /api/seko/<int:seko_id>/albums/<int:pk>/  # アルバム詳細・更新・削除
PUT    /api/seko/<int:seko_id>/albums/<int:pk>/  # アルバム更新
DELETE /api/seko/<int:seko_id>/albums/<int:pk>/  # アルバム削除
GET    /api/seko/<int:seko_id>/share_images/     # 共有画像一覧・作成
POST   /api/seko/<int:seko_id>/share_images/     # 共有画像作成
GET    /api/seko/<int:seko_id>/share_images/<int:pk>/  # 共有画像詳細・更新・削除
PUT    /api/seko/<int:seko_id>/share_images/<int:pk>/  # 共有画像更新
DELETE /api/seko/<int:seko_id>/share_images/<int:pk>/  # 共有画像削除
GET    /api/seko/<int:pk>/pdf/              # 不法書PDF生成
POST   /api/seko/<int:pk>/moshu_mail/       # 喪主案内メール送信
POST   /api/seko/<int:pk>/approve/          # 施行承認
GET    /api/seko/<int:pk>/homeicho/         # 芳名帳一覧
GET    /api/seko/<int:pk>/homeicho/chobun_pdf/    # 芳名帳弔文PDF
GET    /api/seko/<int:pk>/homeicho/kumotsu_pdf/   # 芳名帳供物PDF
GET    /api/seko/<int:pk>/homeicho/koden_pdf/     # 芳名帳香典PDF
GET    /api/seko/<int:pk>/homeicho/msg_pdf/       # 芳名帳メッセージPDF
GET    /api/seko/<int:pk>/msg_pdf/          # メッセージPDF
POST   /api/seko/login/                     # 喪主ログイン
POST   /api/seko/token/refresh/             # 喪主トークンリフレッシュ
POST   /api/seko/token/verify/              # 喪主トークン検証
GET    /api/seko/moshu/                     # 喪主一覧
GET    /api/seko/moshu/<int:pk>/            # 喪主詳細・更新
PUT    /api/seko/moshu/<int:pk>/            # 喪主情報更新
POST   /api/seko/soke/<int:pk>/approve/     # 遺族承認
GET    /api/seko/<int:seko_id>/inquiries/   # 施行問い合わせ一覧・作成
POST   /api/seko/<int:seko_id>/inquiries/   # 施行問い合わせ作成
GET    /api/seko/inquiries/<int:inquiry_id>/answers/  # 問い合わせ回答一覧・作成
POST   /api/seko/inquiries/<int:inquiry_id>/answers/  # 問い合わせ回答作成
POST   /api/seko/fdn/                       # FDN施行作成
PUT    /api/seko/fdn/<int:pk>/              # FDN施行更新
POST   /api/seko/<int:pk>/notice_attached_file/      # 添付ファイル通知
POST   /api/seko/<int:pk>/notice_image_file/soke/     # 遺族向け画像通知
POST   /api/seko/<int:pk>/notice_image_file/company/  # 会社向け画像通知
GET    /api/seko/<int:pk>/qrcode/           # QRコードPDF生成
GET    /api/seko/list/                      # ナウエル施行一覧
```

### 5. orders（注文管理）
```
GET    /api/orders/                        # 注文一覧取得
POST   /api/orders/                        # 注文作成
GET    /api/orders/<int:pk>/               # 注文詳細取得
PUT    /api/orders/<int:pk>/               # 注文更新
GET    /api/orders/<int:pk>/receipt/       # 領収書PDF生成
POST   /api/orders/<int:pk>/cancel/        # 注文キャンセル
GET    /api/orders/detail/<int:pk>/chobun_pdf/  # 弔文PDF生成
GET    /api/orders/kumotsu/                # 供物一覧取得
GET    /api/orders/kumotsu/<int:pk>/       # 供物詳細取得・更新
PUT    /api/orders/kumotsu/<int:pk>/       # 供物更新
POST   /api/orders/kumotsu/order_status/   # 供物注文ステータス更新
GET    /api/orders/kumotsu/<int:pk>/pdf/   # 供物PDF生成
GET    /api/orders/kumotsu/<int:pk>/fax/   # 供物FAX送信
GET    /api/orders/chobun/                 # 弔文一覧取得
GET    /api/orders/chobun/<int:pk>/        # 弔文詳細取得・更新
PUT    /api/orders/chobun/<int:pk>/        # 弔文更新
GET    /api/orders/koden/                  # 香典一覧取得
GET    /api/orders/msg/                    # メッセージ一覧取得
GET    /api/orders/msg/<int:pk>/           # メッセージ詳細取得・更新
PUT    /api/orders/msg/<int:pk>/           # メッセージ更新
GET    /api/orders/fdn/                    # FDN注文一覧
POST   /api/orders/epsilon/return/         # Epsilon決済結果受信
GET    /api/orders/paymentresult/          # 決済結果一覧
```

### 6. henrei（返礼品管理）
```
POST   /api/henrei/add/                    # 返礼品注文作成
GET    /api/henrei/list/                   # 返礼品一覧取得
GET    /api/henrei/orders/                 # 返礼品注文一覧取得
GET    /api/henrei/orders/<int:pk>/        # 返礼品注文詳細取得・更新
PUT    /api/henrei/orders/<int:pk>/        # 返礼品注文更新
GET    /api/henrei/orders/<int:pk>/pdf/    # 返礼品注文PDF生成
GET    /api/henrei/orders/<int:pk>/fax/    # 返礼品注文FAX送信
POST   /api/henrei/orders/order_status/    # 返礼品注文ステータス更新
GET    /api/henrei/koden/                  # 返礼品香典一覧取得
GET    /api/henrei/koden/<int:pk>/         # 返礼品香典詳細取得・更新
PUT    /api/henrei/koden/<int:pk>/         # 返礼品香典更新
GET    /api/henrei/kumotsu/                # 返礼品供物一覧取得
GET    /api/henrei/kumotsu/<int:pk>/       # 返礼品供物詳細取得・更新
PUT    /api/henrei/kumotsu/<int:pk>/       # 返礼品供物更新
POST   /api/henrei/place/                  # 返礼品注文確定
```

### 7. hoyo（法要管理）
```
GET    /api/hoyo/                          # 法要一覧取得
POST   /api/hoyo/                          # 法要作成
GET    /api/hoyo/<int:pk>/                 # 法要詳細取得・更新・削除
PUT    /api/hoyo/<int:pk>/                 # 法要更新
DELETE /api/hoyo/<int:pk>/                 # 法要削除
GET    /api/hoyo/samples/                  # 法要サンプル一覧取得
POST   /api/hoyo/samples/                  # 法要サンプル作成
GET    /api/hoyo/samples/<int:pk>/         # 法要サンプル詳細取得・更新・削除
PUT    /api/hoyo/samples/<int:pk>/         # 法要サンプル更新
DELETE /api/hoyo/samples/<int:pk>/         # 法要サンプル削除
GET    /api/hoyo/mail_templates/           # 法要メールテンプレート一覧取得
POST   /api/hoyo/mail_templates/           # 法要メールテンプレート作成
GET    /api/hoyo/mail_templates/<int:pk>/  # 法要メールテンプレート詳細・更新・削除
PUT    /api/hoyo/mail_templates/<int:pk>/  # 法要メールテンプレート更新
DELETE /api/hoyo/mail_templates/<int:pk>/  # 法要メールテンプレート削除
GET    /api/hoyo/seko/                     # 法要施行一覧取得
POST   /api/hoyo/seko/                     # 法要施行作成
GET    /api/hoyo/seko/<int:pk>/            # 法要施行詳細取得・更新・削除
PUT    /api/hoyo/seko/<int:pk>/            # 法要施行更新
DELETE /api/hoyo/seko/<int:pk>/            # 法要施行削除
GET    /api/hoyo/seko/<int:pk>/pdf/        # 法要PDF生成
GET    /api/hoyo/mails/                    # 法要メール一覧取得
```

### 8. その他モジュール
```
# inquiry（問い合わせ管理）
POST   /api/inquiry/                       # 問い合わせメール送信

# items（商品・サービス管理）
GET    /api/items/                         # 商品一覧取得
POST   /api/items/                         # 商品作成
GET    /api/items/<int:pk>/                # 商品詳細取得・更新・削除
PUT    /api/items/<int:pk>/                # 商品更新
DELETE /api/items/<int:pk>/                # 商品削除
POST   /api/items/bulk_upsert/             # 商品一括更新
POST   /api/items/base/<int:base_id>/      # 商品画像インポート

# suppliers（仕入先管理）
GET    /api/suppliers/                     # 仕入先一覧取得
POST   /api/suppliers/                     # 仕入先作成
GET    /api/suppliers/<int:pk>/            # 仕入先詳細取得・更新・削除
PUT    /api/suppliers/<int:pk>/            # 仕入先更新
DELETE /api/suppliers/<int:pk>/            # 仕入先削除

# invoices（請求書管理）
GET    /api/invoices/sales/                # 売上一覧取得
POST   /api/invoices/sales/<int:pk>/fix/   # 売上確定
POST   /api/invoices/sales/<int:pk>/fix/cancel/  # 売上確定キャンセル
POST   /api/invoices/gather_sales/         # 売上集計
GET    /api/invoices/<int:pk>/pdf/         # 請求書PDF生成

# after_follow（アフターフォロー）
GET    /api/after_follow/groups/           # AFグループ一覧取得
POST   /api/after_follow/groups/           # AFグループ作成
GET    /api/after_follow/groups/all/       # 全AFグループ一覧
GET    /api/after_follow/groups/<int:pk>/  # AFグループ詳細・更新・削除
PUT    /api/after_follow/groups/<int:pk>/  # AFグループ更新
DELETE /api/after_follow/groups/<int:pk>/  # AFグループ削除
GET    /api/after_follow/                  # アフターフォロー一覧取得
POST   /api/after_follow/                  # アフターフォロー作成
GET    /api/after_follow/<int:pk>/         # アフターフォロー詳細・更新・削除
PUT    /api/after_follow/<int:pk>/         # アフターフォロー更新
DELETE /api/after_follow/<int:pk>/         # アフターフォロー削除
GET    /api/after_follow/contact_ways/     # AF連絡方法一覧取得
POST   /api/after_follow/contact_ways/     # AF連絡方法作成
GET    /api/after_follow/contact_ways/<int:pk>/  # AF連絡方法詳細・更新・削除
PUT    /api/after_follow/contact_ways/<int:pk>/  # AF連絡方法更新
DELETE /api/after_follow/contact_ways/<int:pk>/  # AF連絡方法削除
GET    /api/after_follow/sekoaf/           # 施行AF一覧取得
POST   /api/after_follow/sekoaf/           # 施行AF作成
GET    /api/after_follow/sekoaf/<int:pk>/  # 施行AF詳細・更新・削除
PUT    /api/after_follow/sekoaf/<int:pk>/  # 施行AF更新
DELETE /api/after_follow/sekoaf/<int:pk>/  # 施行AF削除
GET    /api/after_follow/wish/<int:pk>/    # AF希望詳細・更新
PUT    /api/after_follow/wish/<int:pk>/    # AF希望更新
GET    /api/after_follow/activity/         # AF活動一覧取得
POST   /api/after_follow/activity/         # AF活動作成
GET    /api/after_follow/activity/<int:pk>/  # AF活動詳細・更新・削除
PUT    /api/after_follow/activity/<int:pk>/  # AF活動更新
DELETE /api/after_follow/activity/<int:pk>/  # AF活動削除

# event_mails（イベントメール）
GET    /api/event_mails/                   # イベントメール一覧取得

# faqs（FAQ管理）
GET    /api/faqs/                          # FAQ一覧取得
POST   /api/faqs/                          # FAQ作成
GET    /api/faqs/<int:pk>/                 # FAQ詳細取得・更新・削除
PUT    /api/faqs/<int:pk>/                 # FAQ更新
DELETE /api/faqs/<int:pk>/                 # FAQ削除

# advertises（広告管理）
GET    /api/advertises/                    # 広告一覧取得
POST   /api/advertises/                    # 広告作成
GET    /api/advertises/<int:pk>/           # 広告詳細取得・更新・削除
PUT    /api/advertises/<int:pk>/           # 広告更新
DELETE /api/advertises/<int:pk>/           # 広告削除

# fuho_samples（不法サンプル）
GET    /api/fuho_samples/                  # 不法サンプル一覧取得
POST   /api/fuho_samples/                  # 不法サンプル作成
GET    /api/fuho_samples/<int:pk>/         # 不法サンプル詳細取得・更新・削除
PUT    /api/fuho_samples/<int:pk>/         # 不法サンプル更新
DELETE /api/fuho_samples/<int:pk>/         # 不法サンプル削除

# chobun_daishi（弔文台紙）
GET    /api/chobun_daishi/                 # 弔文台紙一覧取得

# service_reception_terms（サービス受付条件）
GET    /api/service_reception_terms/       # サービス受付条件一覧取得
```

## 認証・認可フロー

### 詳細認証・権限フロー

```mermaid
graph TD
    subgraph "ユーザータイプ別認証"
        A1[システム管理者<br/>ADMIN]
        A2[葬儀社管理者<br/>COMPANY]
        A3[部門管理者<br/>DEPARTMENT]
        A4[式場スタッフ<br/>HALL]
        A5[その他<br/>OTHER]
        B1[顧客<br/>匿名ユーザー]
        C1[遺族<br/>喪主認証]
    end

    subgraph "認証ガード"
        G1[AuthFGuard<br/>スタッフ認証]
        G2[AuthSGuard<br/>遺族認証]
        G3[AdminGuard<br/>管理者権限]
        G4[StaffOnlyGuard<br/>スタッフ専用]
        G5[OrderGuard<br/>注文権限]
    end

    subgraph "アクセス可能機能"
        F1[全機能<br/>システム管理]
        F2[葬儀社機能<br/>業務管理]
        F3[部門機能<br/>施行管理]
        F4[式場機能<br/>現場業務]
        F5[顧客機能<br/>注文・問い合わせ]
        F6[遺族機能<br/>施行情報閲覧]
    end

    A1 --> G1
    A1 --> G3
    A2 --> G1
    A2 --> G3
    A3 --> G1
    A3 --> G4
    A4 --> G1
    A4 --> G4
    A5 --> G1
    B1 --> G5
    C1 --> G2

    G1 --> F1
    G1 --> F2
    G1 --> F3
    G1 --> F4
    G3 --> F1
    G3 --> F2
    G4 --> F3
    G4 --> F4
    G5 --> F5
    G2 --> F6
```

### Company画面の詳細権限分析

```mermaid
graph TD
    subgraph "システム管理者専用機能"
        S1[拠点管理<br/>base]
        S2[仕入先管理<br/>supplier]
        S3[スタッフ管理<br/>staff]
        S4[不法サンプル管理<br/>fuho-sample]
        S5[弔文台紙管理<br/>chobun-daishi]
        S6[商品サービス管理<br/>item-service]
        S7[広告管理<br/>advertise]
        S8[FAQ管理<br/>faq]
    end

    subgraph "葬儀社・システム共通機能"
        C1[施行管理<br/>seko, seko-edit]
        C2[返礼品管理<br/>henreihin, order-henreihin]
        C3[弔文受付<br/>chobun]
        C4[供物管理<br/>kumotsu]
        C5[香典管理<br/>koden, koden-summary]
        C6[メッセージ管理<br/>message]
        C7[注文キャンセル<br/>order-cancel]
        C8[月次集計<br/>monthly-summary, monthly-confirm]
        C9[売上集計<br/>sales-summary]
        C10[請求書<br/>invoice]
        C11[法要管理<br/>hoyo-master, hoyo-sample, hoyo-mail-template]
        C12[アフターフォロー<br/>af-group, af-contact, seko-af]
        C13[イベント管理<br/>event-guidance, event-filter]
        C14[法要フィルタ<br/>hoyo-filter-ki, hoyo-filter-bon]
        C15[法要案内<br/>hoyo-guidance]
        C16[遺族メニュー<br/>soke-menu]
    end

    subgraph "権限レベル"
        ADMIN[ADMIN<br/>システム管理者]
        COMPANY[COMPANY<br/>葬儀社管理者]
        DEPT[DEPARTMENT<br/>部門管理者]
        HALL[HALL<br/>式場スタッフ]
    end

    ADMIN --> S1
    ADMIN --> S2
    ADMIN --> S3
    ADMIN --> S4
    ADMIN --> S5
    ADMIN --> S6
    ADMIN --> S7
    ADMIN --> S8
    ADMIN --> C1
    ADMIN --> C2
    ADMIN --> C3
    ADMIN --> C4
    ADMIN --> C5
    ADMIN --> C6
    ADMIN --> C7
    ADMIN --> C8
    ADMIN --> C9
    ADMIN --> C10
    ADMIN --> C11
    ADMIN --> C12
    ADMIN --> C13
    ADMIN --> C14
    ADMIN --> C15
    ADMIN --> C16

    COMPANY --> C1
    COMPANY --> C2
    COMPANY --> C3
    COMPANY --> C4
    COMPANY --> C5
    COMPANY --> C6
    COMPANY --> C7
    COMPANY --> C8
    COMPANY --> C9
    COMPANY --> C10
    COMPANY --> C11
    COMPANY --> C12
    COMPANY --> C13
    COMPANY --> C14
    COMPANY --> C15
    COMPANY --> C16

    DEPT --> C1
    DEPT --> C2
    DEPT --> C3
    DEPT --> C4
    DEPT --> C5
    DEPT --> C6

    HALL --> C1
    HALL --> C2
    HALL --> C3
    HALL --> C4
    HALL --> C5
    HALL --> C6
```

## 全エンティティ・モデル一覧

### 1. コアエンティティ
```
bases.Base              # 拠点情報（階層構造）
bases.Tokusho           # 特定商取引法情報
bases.FocFee            # FOC手数料設定
bases.SmsAccount        # SMS設定

staffs.Staff            # スタッフ情報

seko.Seko               # 施行基本情報
seko.Kojin              # 故人情報
seko.Moshu              # 喪主情報
seko.SekoSchedule       # 施行スケジュール
seko.SekoItem           # 施行商品
seko.SekoService        # 施行サービス
seko.SekoAlbum          # 施行アルバム
seko.SekoVideo          # 施行動画
seko.SekoInquiry        # 施行問い合わせ
seko.SekoAnswer         # 施行回答
seko.SekoContact        # 施行連絡先
seko.SekoShareImage     # 施行共有画像
seko.HenreihinKakegami  # 返礼品掛紙
```

### 2. マスタエンティティ
```
masters.Schedule            # 日程項目マスタ
masters.SekoStyle          # 施行スタイルマスタ
masters.ZipCode            # 郵便番号マスタ
masters.Wareki             # 和暦マスタ
masters.FuhoSampleMaster   # 不法サンプルマスタ
masters.ChobunDaishiMaster # 弔文代師マスタ
masters.ChobunSample       # 弔文サンプルマスタ
masters.Service            # サービスマスタ
masters.Tax                # 税率マスタ
masters.Relationship       # 関係性マスタ
masters.HenreihinParameter # 返礼品パラメータ
masters.KodenParameter     # 香典パラメータ
masters.HoyoStyle          # 法要スタイルマスタ
masters.HoyoDefaultMaster  # 法要デフォルトマスタ
masters.HoyoSampleMaster   # 法要サンプルマスタ
masters.SokeMenu           # 遺族メニューマスタ
```

### 3. 業務エンティティ
```
orders.Entry            # 注文エントリ
orders.EntryDetail      # 注文詳細
orders.PaymentResult    # 決済結果

henrei.OrderHenrei      # 返礼品注文
henrei.HenreiKoden      # 返礼品香典
henrei.HenreiKumotsu    # 返礼品供物

hoyo.Hoyo               # 法要情報
hoyo.HoyoSeko           # 法要施行関連
hoyo.HoyoSchedule       # 法要スケジュール
hoyo.HoyoSample         # 法要サンプル
hoyo.HoyoMail           # 法要メール
hoyo.HoyoMailTemplate   # 法要メールテンプレート
hoyo.HoyoMailTarget     # 法要メール対象

after_follow.SekoAf         # 施行アフターフォロー
after_follow.AfGroup        # アフターフォローグループ
after_follow.AfContactWay   # アフター連絡方法
after_follow.AfActivityDetail # アフター活動詳細
after_follow.AfWish         # アフター希望

items.Item              # 商品・サービス情報
suppliers.Supplier      # 仕入先情報
invoices.Sales          # 売上情報
event_mails.EventMail   # イベントメール
faqs.Faq                # FAQ
advertises.Advertise    # 広告
fuho_samples.FuhoSample # 不法サンプル
chobun_daishi.ChobunDaishi # 弔文台紙
service_reception_terms.ServiceReceptionTerm # サービス受付条件
```

## ファイル・メディア管理

```mermaid
graph TD
    subgraph "ファイルアップロード"
        A1[画像ファイル<br/>jpg, png, gif]
        A2[動画ファイル<br/>mp4, avi]
        A3[PDFファイル<br/>帳票・書類]
        A4[その他ファイル<br/>添付資料]
    end

    subgraph "ストレージ管理"
        B1[SekoAlbum<br/>施行アルバム]
        B2[SekoVideo<br/>施行動画]
        B3[SekoShareImage<br/>共有画像]
        B4[Invoice<br/>請求書PDF]
        B5[Receipt<br/>領収書PDF]
        B6[Report<br/>各種帳票PDF]
    end

    subgraph "アクセス制御"
        C1[Company<br/>葬儀社・システム会社]
        C2[Family<br/>遺族]
        C3[Customer<br/>顧客]
    end

    A1 --> B1
    A1 --> B3
    A2 --> B2
    A3 --> B4
    A3 --> B5
    A3 --> B6
    A4 --> B1

    B1 --> C1
    B1 --> C2
    B2 --> C1
    B2 --> C2
    B3 --> C1
    B3 --> C2
    B4 --> C1
    B5 --> C1
    B5 --> C3
    B6 --> C1
```
