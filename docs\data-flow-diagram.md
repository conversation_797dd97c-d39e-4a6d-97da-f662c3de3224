# FOCプロジェクト データフロー図

## システム全体データフロー

```mermaid
graph TB
    subgraph "フロントエンド (Angular)"
        A[Company<br/>葬儀社画面]
        B[Customer<br/>顧客画面]
        C[Family<br/>遺族画面]
    end
    
    subgraph "バックエンド (Django REST API)"
        D[認証システム<br/>JWT]
        E[API Gateway<br/>urls.py]
        
        subgraph "コアモジュール"
            F[masters<br/>マスタ管理]
            G[bases<br/>拠点管理]
            H[seko<br/>施行管理]
        end
        
        subgraph "業務モジュール"
            I[orders<br/>注文管理]
            J[henrei<br/>返礼品管理]
            K[hoyo<br/>法要管理]
            L[after_follow<br/>アフターフォロー]
        end
        
        subgraph "サポートモジュール"
            M[inquiry<br/>問い合わせ]
            N[invoices<br/>請求書]
            O[event_mails<br/>メール配信]
        end
    end
    
    subgraph "外部システム"
        P[PostgreSQL<br/>データベース]
        Q[Epsilon<br/>決済システム]
        R[SendGrid<br/>メール配信]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    E --> G
    E --> H
    E --> I
    E --> J
    E --> K
    E --> L
    E --> M
    E --> N
    E --> O
    
    F --> P
    G --> P
    H --> P
    I --> P
    I --> Q
    O --> R
```

## 主要業務フロー

### 1. 施行管理フロー

```mermaid
sequenceDiagram
    participant CS as 葬儀社スタッフ
    participant API as Django API
    participant DB as PostgreSQL
    participant FM as 遺族
    
    CS->>API: 施行情報登録
    API->>DB: seko, kojin, moshu作成
    DB-->>API: 施行ID返却
    API-->>CS: 登録完了
    
    CS->>API: 遺族認証情報設定
    API->>DB: moshu認証情報更新
    
    FM->>API: 遺族ログイン
    API->>DB: 認証情報確認
    DB-->>API: 認証成功
    API-->>FM: JWT発行
    
    FM->>API: 施行情報閲覧
    API->>DB: 施行情報取得
    DB-->>API: 施行データ
    API-->>FM: 施行情報表示
```

### 2. 注文管理フロー

```mermaid
sequenceDiagram
    participant CU as 顧客
    participant API as Django API
    participant DB as PostgreSQL
    participant EP as Epsilon決済
    participant CS as 葬儀社
    
    CU->>API: 商品選択・カート追加
    API->>DB: カート情報保存
    
    CU->>API: 注文確定
    API->>DB: Entry, EntryDetail作成
    
    CU->>API: 決済処理
    API->>EP: 決済要求
    EP-->>API: 決済結果
    API->>DB: PaymentResult保存
    API-->>CU: 注文完了
    
    API->>CS: 注文通知メール
    CS->>API: 注文確認・処理
```

### 3. 法要管理フロー

```mermaid
sequenceDiagram
    participant CU as 顧客
    participant API as Django API
    participant DB as PostgreSQL
    participant MAIL as SendGrid
    participant CS as 葬儀社
    
    CU->>API: 法要申込
    API->>DB: Hoyo, HoyoSeko作成
    
    CS->>API: 法要承認
    API->>DB: 法要ステータス更新
    
    API->>DB: メールテンプレート取得
    API->>MAIL: 法要案内メール送信
    MAIL-->>CU: 法要案内受信
    
    API->>DB: HoyoSchedule作成
    CS->>API: 法要実施報告
```

## モジュール間依存関係

```mermaid
graph TD
    A[masters] --> B[bases]
    A --> C[seko]
    A --> D[hoyo]
    A --> E[orders]
    
    B --> C
    B --> F[staffs]
    
    C --> G[henrei]
    C --> D
    C --> H[after_follow]
    C --> I[inquiry]
    
    E --> G
    E --> J[invoices]
    
    D --> K[event_mails]
    H --> K
    
    L[suppliers] --> E
    M[items] --> E
    M --> C
    
    N[faqs] --> O[advertises]
```

## データベース関係図

```mermaid
erDiagram
    Base ||--o{ Seko : "拠点-施行"
    Base ||--o{ Staff : "拠点-スタッフ"
    Base {
        int id PK
        int base_type
        int parent_id FK
        string base_name
        string company_code
    }
    
    Seko ||--|| Kojin : "施行-故人"
    Seko ||--|| Moshu : "施行-喪主"
    Seko ||--o{ SekoSchedule : "施行-スケジュール"
    Seko ||--o{ SekoItem : "施行-商品"
    Seko ||--o{ SekoService : "施行-サービス"
    Seko {
        int id PK
        int base_id FK
        string seko_no
        date seko_date
        int seko_style_id FK
    }
    
    Kojin {
        int id PK
        int seko_id FK
        string kojin_name
        date birth_date
        date death_date
    }
    
    Moshu {
        int id PK
        int seko_id FK
        string moshu_name
        string tel
        string email
        string password
    }
    
    Entry ||--o{ EntryDetail : "注文-詳細"
    Entry {
        int id PK
        int seko_id FK
        int base_id FK
        datetime entry_datetime
        int total_amount
    }
    
    EntryDetail {
        int id PK
        int entry_id FK
        int item_id FK
        int quantity
        int unit_price
    }
    
    Hoyo ||--|| HoyoSeko : "法要-施行"
    Hoyo {
        int id PK
        int base_id FK
        string hoyo_name
        date hoyo_date
        int hoyo_style_id FK
    }
    
    HoyoSeko {
        int id PK
        int hoyo_id FK
        int seko_id FK
    }
```

## API エンドポイント構造

```
/api/
├── masters/              # マスタデータ
│   ├── schedules/       # 日程項目
│   ├── seko_styles/     # 施行スタイル
│   ├── zipcode/         # 郵便番号
│   └── services/        # サービス
├── bases/               # 拠点管理
│   ├── all/            # 全拠点一覧
│   ├── <id>/           # 拠点詳細
│   └── <id>/tokusho/   # 特商法情報
├── seko/                # 施行管理
│   ├── /               # 施行一覧・作成
│   ├── <id>/           # 施行詳細
│   ├── <id>/kojin/     # 故人情報
│   ├── <id>/moshu/     # 喪主情報
│   └── <id>/schedules/ # スケジュール
├── orders/              # 注文管理
│   ├── /               # 注文一覧・作成
│   ├── <id>/           # 注文詳細
│   └── <id>/payment/   # 決済処理
├── hoyo/                # 法要管理
│   ├── /               # 法要一覧・作成
│   ├── <id>/           # 法要詳細
│   └── <id>/mail/      # メール送信
└── inquiry/             # 問い合わせ
    ├── /               # 問い合わせ一覧・作成
    └── <id>/           # 問い合わせ詳細
```

## 認証・認可フロー

```mermaid
graph TD
    A[ユーザーアクセス] --> B{ユーザータイプ}
    
    B -->|葬儀社スタッフ| C[スタッフログイン]
    B -->|顧客| D[顧客画面アクセス]
    B -->|遺族| E[遺族認証]
    
    C --> F[JWT発行<br/>staff権限]
    D --> G[匿名アクセス<br/>一部機能制限]
    E --> H[JWT発行<br/>family権限]
    
    F --> I[Company画面<br/>全機能アクセス]
    G --> J[Customer画面<br/>注文・問い合わせ]
    H --> K[Family画面<br/>施行情報閲覧]
    
    I --> L[API認証<br/>IsAuthenticated]
    J --> M[API認証<br/>匿名・制限付き]
    K --> L
```

## ファイル・メディア管理

```mermaid
graph TD
    A[ファイルアップロード] --> B{ファイルタイプ}
    
    B -->|画像| C[SekoAlbum<br/>施行アルバム]
    B -->|動画| D[SekoVideo<br/>施行動画]
    B -->|PDF| E[Invoice<br/>請求書]
    B -->|共有画像| F[SekoShareImage<br/>共有画像]
    
    C --> G[メディアストレージ]
    D --> G
    E --> H[PDFストレージ]
    F --> G
    
    G --> I[Family画面<br/>閲覧・ダウンロード]
    H --> J[Company画面<br/>帳票出力]
```
