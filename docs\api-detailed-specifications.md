# FOC API 詳細仕様書

## 1. POST /api/staffs/login/ - スタッフログイン

### 基本情報
- **機能**: スタッフの認証とJWTトークンの発行
- **HTTPメソッド**: POST
- **認証**: 不要（認証API）
- **Content-Type**: application/json

### リクエストパラメータ

#### 必須パラメータ
| パラメータ名 | 型 | 必須 | 最大桁数 | フォーマット | 説明 |
|-------------|---|------|----------|-------------|------|
| company_code | string | ✓ | 20 | 英数字 | 会社コード |
| login_id | string | ✓ | 20 | 英数字・記号 | ログインID |
| password | string | ✓ | - | 任意文字列 | パスワード |

#### バリデーション規則
- **company_code**: 
  - 必須チェック
  - 最大20文字
  - 空文字不可
- **login_id**: 
  - 必須チェック
  - 最大20文字
  - 空文字不可
- **password**: 
  - 必須チェック
  - 空文字不可

### リクエスト例
```json
{
  "company_code": "COMP001",
  "login_id": "staff001",
  "password": "SecurePass123!"
}
```

### レスポンスパラメータ

#### 成功時（200 OK）
| パラメータ名 | 型 | 説明 |
|-------------|---|------|
| access | string | アクセストークン（JWT、有効期限1時間） |
| refresh | string | リフレッシュトークン（JWT、有効期限7日） |

### レスポンス例

#### 成功時
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.**************************************************************************************************************************************************.signature",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.***************************************************************************************************************************************************.signature"
}
```

### エラーレスポンス

#### 400 Bad Request - バリデーションエラー
```json
{
  "company_code": ["この項目は必須です。"],
  "login_id": ["この項目は必須です。"],
  "password": ["この項目は必須です。"]
}
```

#### 401 Unauthorized - 認証失敗
```json
{
  "detail": "No active account found with the given credentials"
}
```

### エラーコード一覧
| HTTPステータス | エラーコード | 説明 |
|---------------|-------------|------|
| 400 | VALIDATION_ERROR | リクエストパラメータのバリデーションエラー |
| 401 | AUTHENTICATION_FAILED | 認証情報が無効（会社コード、ログインID、パスワードの組み合わせが不正） |
| 401 | ACCOUNT_DISABLED | アカウントが無効化されている |
| 401 | ACCOUNT_RETIRED | アカウントが退職済み |

### 認証ロジック
1. company_code + login_id + password の組み合わせで認証
2. スタッフの del_flg = false かつ retired_flg = false をチェック
3. パスワードハッシュの照合
4. 認証成功時、last_login を更新
5. JWT トークンペアを生成・返却

---

## 2. POST /api/staffs/token/refresh/ - トークンリフレッシュ

### 基本情報
- **機能**: リフレッシュトークンを使用してアクセストークンを再発行
- **HTTPメソッド**: POST
- **認証**: リフレッシュトークン
- **Content-Type**: application/json

### リクエストパラメータ

#### 必須パラメータ
| パラメータ名 | 型 | 必須 | フォーマット | 説明 |
|-------------|---|------|-------------|------|
| refresh | string | ✓ | JWT形式 | リフレッシュトークン |

#### バリデーション規則
- **refresh**: 
  - 必須チェック
  - JWT形式の検証
  - 有効期限チェック
  - ブラックリストチェック

### リクエスト例
```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.***************************************************************************************************************************************************.signature"
}
```

### レスポンスパラメータ

#### 成功時（200 OK）
| パラメータ名 | 型 | 説明 |
|-------------|---|------|
| access | string | 新しいアクセストークン（JWT、有効期限1時間） |

### レスポンス例

#### 成功時
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzA0MDcwODAwLCJpYXQiOjE3MDQwNjcyMDAsImp0aSI6IjExMTExMTExMTEiLCJ1c2VyX2lkIjoxLCJ1c2VyX2NsYXNzIjoiU3RhZmYifQ.signature"
}
```

### エラーレスポンス

#### 400 Bad Request - バリデーションエラー
```json
{
  "refresh": ["この項目は必須です。"]
}
```

#### 401 Unauthorized - トークンエラー
```json
{
  "detail": "Token is invalid or expired",
  "code": "token_not_valid"
}
```

### エラーコード一覧
| HTTPステータス | エラーコード | 説明 |
|---------------|-------------|------|
| 400 | VALIDATION_ERROR | リクエストパラメータのバリデーションエラー |
| 401 | TOKEN_NOT_VALID | トークンが無効または期限切れ |
| 401 | TOKEN_BLACKLISTED | トークンがブラックリストに登録済み |

---

## 3. POST /api/seko/fdn/ - FDN施行作成

### 基本情報
- **機能**: FDN（Funeral Director Network）システム向けの施行データ作成
- **HTTPメソッド**: POST
- **認証**: Bearer Token（スタッフ認証）
- **Content-Type**: application/json

### 認証方法
```
Authorization: Bearer {access_token}
```

### リクエストパラメータ

#### 必須パラメータ
| パラメータ名 | 型 | 必須 | 最大桁数 | フォーマット | 説明 |
|-------------|---|------|----------|-------------|------|
| base_id | integer | ✓ | - | 正整数 | 拠点ID |
| seko_style | integer | ✓ | - | 正整数 | 施行スタイルID |
| kojin.kojin_name | string | ✓ | 100 | 任意文字列 | 故人名 |
| kojin.death_date | string | ✓ | 10 | YYYY-MM-DD | 死亡日 |
| moshu.moshu_name | string | ✓ | 100 | 任意文字列 | 喪主名 |
| schedules | array | ✓ | - | オブジェクト配列 | スケジュール配列 |

#### オプションパラメータ
| パラメータ名 | 型 | 必須 | 最大桁数 | フォーマット | 説明 |
|-------------|---|------|----------|-------------|------|
| seko_date | string | - | 10 | YYYY-MM-DD | 施行日 |
| seko_place | string | - | 200 | 任意文字列 | 施行場所 |
| kojin.kojin_kana | string | - | 100 | カタカナ | 故人名カナ |
| kojin.birth_date | string | - | 10 | YYYY-MM-DD | 生年月日 |
| kojin.age | integer | - | 3 | 正整数 | 年齢 |
| kojin.gender | integer | - | 1 | 1または2 | 性別（1:男性、2:女性） |
| moshu.moshu_kana | string | - | 100 | カタカナ | 喪主名カナ |
| moshu.tel | string | - | 20 | 電話番号形式 | 電話番号 |
| moshu.email | string | - | 254 | メール形式 | メールアドレス |

#### schedules配列の要素
| パラメータ名 | 型 | 必須 | 説明 |
|-------------|---|------|------|
| schedule_id | integer | ✓ | スケジュールID |
| schedule_date | string | ✓ | 日程（YYYY-MM-DD） |
| begin_time | string | - | 開始時間（HH:MM） |
| end_time | string | - | 終了時間（HH:MM） |
| place | string | - | 場所 |

### リクエスト例
```json
{
  "base_id": 2,
  "seko_date": "2024-01-15",
  "seko_style": 1,
  "seko_place": "○○会館",
  "kojin": {
    "kojin_name": "山田太郎",
    "kojin_kana": "ヤマダタロウ",
    "birth_date": "1950-01-01",
    "death_date": "2024-01-10",
    "age": 74,
    "gender": 1
  },
  "moshu": {
    "moshu_name": "山田花子",
    "moshu_kana": "ヤマダハナコ",
    "tel": "090-1234-5678",
    "email": "<EMAIL>"
  },
  "schedules": [
    {
      "schedule_id": 1,
      "schedule_date": "2024-01-14",
      "begin_time": "18:00",
      "end_time": "20:00",
      "place": "○○会館"
    },
    {
      "schedule_id": 2,
      "schedule_date": "2024-01-15",
      "begin_time": "10:00",
      "end_time": "12:00",
      "place": "○○会館"
    }
  ]
}
```

### レスポンスパラメータ

#### 成功時（201 Created）
| パラメータ名 | 型 | 説明 |
|-------------|---|------|
| status | integer | ステータス（0:成功、1:エラー） |
| data.seko_id | integer | 作成された施行ID |

### レスポンス例

#### 成功時
```json
{
  "status": 0,
  "data": {
    "seko_id": 123
  }
}
```

#### バリデーションエラー時
```json
{
  "status": 1,
  "error": {
    "kojin": {
      "kojin_name": ["この項目は必須です。"]
    },
    "moshu": {
      "moshu_name": ["この項目は必須です。"]
    }
  }
}
```

### エラーコード一覧
| HTTPステータス | エラーコード | 説明 |
|---------------|-------------|------|
| 400 | VALIDATION_ERROR | リクエストパラメータのバリデーションエラー |
| 401 | AUTHENTICATION_REQUIRED | 認証が必要 |
| 403 | PERMISSION_DENIED | 権限不足（スタッフ権限が必要） |
| 404 | BASE_NOT_FOUND | 指定された拠点IDが存在しない |
| 404 | SEKO_STYLE_NOT_FOUND | 指定された施行スタイルIDが存在しない |

---

## 4. PUT /api/seko/fdn/<seko_id>/ - FDN施行更新

### 基本情報
- **機能**: FDN（Funeral Director Network）システム向けの施行データ更新
- **HTTPメソッド**: PUT
- **認証**: Bearer Token（スタッフ認証）
- **Content-Type**: application/json

### 認証方法
```
Authorization: Bearer {access_token}
```

### URLパラメータ
| パラメータ名 | 型 | 必須 | 説明 |
|-------------|---|------|------|
| seko_id | integer | ✓ | 更新対象の施行ID |

### リクエストパラメータ

#### 必須パラメータ
| パラメータ名 | 型 | 必須 | 最大桁数 | フォーマット | 説明 |
|-------------|---|------|----------|-------------|------|
| fdn_code | string | ✓ | 50 | 英数字 | FDNシステムの施行コード |

#### バリデーション規則
- **fdn_code**:
  - 必須チェック
  - 最大50文字
  - 空文字不可
  - 英数字・ハイフン・アンダースコア許可

### リクエスト例
```json
{
  "fdn_code": "FDN-2024-001-COMP001"
}
```

### レスポンスパラメータ

#### 成功時（200 OK）
| パラメータ名 | 型 | 説明 |
|-------------|---|------|
| id | integer | 施行ID |
| fdn_code | string | 更新されたFDNコード |

### レスポンス例

#### 成功時
```json
{
  "id": 123,
  "fdn_code": "FDN-2024-001-COMP001"
}
```

### エラーレスポンス

#### 400 Bad Request - バリデーションエラー
```json
{
  "fdn_code": ["この項目は必須です。"]
}
```

#### 404 Not Found - 施行が存在しない
```json
{
  "detail": "見つかりません。"
}
```

### エラーコード一覧
| HTTPステータス | エラーコード | 説明 |
|---------------|-------------|------|
| 400 | VALIDATION_ERROR | リクエストパラメータのバリデーションエラー |
| 401 | AUTHENTICATION_REQUIRED | 認証が必要 |
| 403 | PERMISSION_DENIED | 権限不足（スタッフ権限が必要） |
| 404 | SEKO_NOT_FOUND | 指定された施行IDが存在しない |

---

## 5. GET /api/orders/fdn/ - FDN注文一覧取得

### 基本情報
- **機能**: FDN（Funeral Director Network）システム向けの注文一覧を取得
- **HTTPメソッド**: GET
- **認証**: Bearer Token（認証済みユーザー）
- **Content-Type**: application/json

### 認証方法
```
Authorization: Bearer {access_token}
```

### クエリパラメータ

#### オプションパラメータ
| パラメータ名 | 型 | 必須 | フォーマット | 説明 |
|-------------|---|------|-------------|------|
| term_from | string | - | YYYY-MM-DDTHH:MM:SS | 期間From（日時） |
| term_to | string | - | YYYY-MM-DDTHH:MM:SS | 期間To（日時） |
| page | integer | - | 正整数 | ページ番号（デフォルト: 1） |
| page_size | integer | - | 正整数 | 1ページあたりの件数（デフォルト: 20） |

### リクエスト例
```
GET /api/orders/fdn/?term_from=2024-01-01T00:00:00&term_to=2024-01-31T23:59:59&page=1&page_size=10
```

### レスポンスパラメータ

#### 成功時（200 OK）
| パラメータ名 | 型 | 説明 |
|-------------|---|------|
| count | integer | 総件数 |
| next | string | 次ページのURL（null: 次ページなし） |
| previous | string | 前ページのURL（null: 前ページなし） |
| results | array | 注文データ配列 |

#### results配列の要素
| パラメータ名 | 型 | 説明 |
|-------------|---|------|
| seko_id | integer | 施行ID |
| fdn_seko_code | string | FDN施行コード |
| soke_name | string | 遺族名 |
| tsuya_hall_name | string | 通夜会場名 |
| tsuya_date | string | 通夜日（YYYY-MM-DD） |
| tsuya_begin_time | string | 通夜開始時間（HH:MM:SS） |
| sogi_hall_name | string | 葬儀会場名 |
| sogi_date | string | 葬儀日（YYYY-MM-DD） |
| sogi_begin_time | string | 葬儀開始時間（HH:MM:SS） |
| kojin_name | string | 故人名 |
| kojin_birth_date | string | 故人生年月日（YYYY-MM-DD） |
| kojin_death_date | string | 故人死亡日（YYYY-MM-DD） |
| moshu_name | string | 喪主名 |
| moshu_tel | string | 喪主電話番号 |
| moshu_mobile | string | 喪主携帯番号 |
| entry_id | integer | 注文ID |
| entry_name | string | 注文者名 |
| entry_name_kana | string | 注文者名カナ |

### レスポンス例

#### 成功時
```json
{
  "count": 25,
  "next": "http://api/orders/fdn/?page=2&term_from=2024-01-01T00:00:00&term_to=2024-01-31T23:59:59",
  "previous": null,
  "results": [
    {
      "seko_id": 123,
      "fdn_seko_code": "FDN-2024-001-COMP001",
      "soke_name": "山田家",
      "tsuya_hall_name": "○○会館 1F式場",
      "tsuya_date": "2024-01-14",
      "tsuya_begin_time": "18:00:00",
      "sogi_hall_name": "○○会館 1F式場",
      "sogi_date": "2024-01-15",
      "sogi_begin_time": "10:00:00",
      "kojin_name": "山田太郎",
      "kojin_birth_date": "1950-01-01",
      "kojin_death_date": "2024-01-10",
      "moshu_name": "山田花子",
      "moshu_tel": "03-1234-5678",
      "moshu_mobile": "090-1234-5678",
      "entry_id": 456,
      "entry_name": "田中一郎",
      "entry_name_kana": "タナカイチロウ"
    }
  ]
}
```

### エラーレスポンス

#### 400 Bad Request - パラメータエラー
```json
{
  "term_from": ["日時の形式が正しくありません。"]
}
```

#### 401 Unauthorized - 認証エラー
```json
{
  "detail": "認証情報が提供されていません。"
}
```

### エラーコード一覧
| HTTPステータス | エラーコード | 説明 |
|---------------|-------------|------|
| 400 | VALIDATION_ERROR | クエリパラメータのバリデーションエラー |
| 401 | AUTHENTICATION_REQUIRED | 認証が必要 |
| 403 | PERMISSION_DENIED | 権限不足 |

---

## 共通仕様

### JWT認証
- **アルゴリズム**: RS256（RSA署名）
- **ヘッダー形式**: `Authorization: Bearer {access_token}`
- **アクセストークン有効期限**: 1時間
- **リフレッシュトークン有効期限**: 7日間

### JWTペイロード構造
```json
{
  "token_type": "access",
  "exp": 1704067200,
  "iat": 1704063600,
  "jti": "1234567890",
  "user_id": 1,
  "user_class": "Staff"
}
```

### 日時フォーマット
- **日付**: YYYY-MM-DD（例: 2024-01-15）
- **時刻**: HH:MM:SS（例: 18:00:00）
- **日時**: YYYY-MM-DDTHH:MM:SS（例: 2024-01-15T18:00:00）
- **タイムゾーン**: UTC

### ページネーション
- **デフォルトページサイズ**: 20件
- **最大ページサイズ**: 100件
- **ページ番号**: 1から開始

### エラーレスポンス共通形式
```json
{
  "field_name": ["エラーメッセージ"],
  "detail": "詳細エラーメッセージ",
  "code": "エラーコード"
}
```

### HTTPステータスコード
- **200**: 成功
- **201**: 作成成功
- **400**: リクエストエラー
- **401**: 認証エラー
- **403**: 権限エラー
- **404**: リソースが見つからない
- **500**: サーバーエラー
