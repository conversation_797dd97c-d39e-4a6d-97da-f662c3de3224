# FOCプロジェクト ドキュメント

## 概要

このディレクトリには、FOC（Funeral Online system Connect）プロジェクトの詳細な分析ドキュメントが含まれています。

## ドキュメント一覧

### 1. [プロジェクト分析](./project-analysis.md)
- プロジェクト全体の概要
- 技術スタック
- アーキテクチャ構造
- 主要ディレクトリ構造
- データフロー概要
- 特徴的な機能

### 2. [モジュール詳細仕様](./module-details.md)
- バックエンドモジュール詳細
  - masters（マスタデータ管理）
  - bases（拠点・組織管理）
  - seko（施行管理）
  - orders（注文管理）
  - hoyo（法要管理）
  - その他業務モジュール
- フロントエンドページ詳細
  - Company（葬儀社向け）
  - Customer（顧客向け）
  - Family（遺族向け）
- 共通機能・コンポーネント

### 3. [データフロー図](./data-flow-diagram.md)
- システム全体データフロー
- 主要業務フロー
  - 施行管理フロー
  - 注文管理フロー
  - 法要管理フロー
- モジュール間依存関係
- データベース関係図
- API エンドポイント構造
- 認証・認可フロー

### 4. [技術仕様書](./technical-specifications.md)
- システム要件
- アーキテクチャ設計
- データベース設計
- API設計
- フロントエンド設計
- セキュリティ設計
- パフォーマンス最適化
- 運用・監視

### 5. [Company画面機能詳細分析](./company-features-analysis.md)
- システム管理者専用機能（ADMIN）
- 葬儀社・システム共通機能
- 権限レベル別アクセス制御
- データアクセス制御の実装

## プロジェクト構造概要

```
FOC (Funeral Online system Connect)
├── foc-backend/          # Django REST APIバックエンド
│   ├── foc/             # Django設定・メインアプリ
│   ├── masters/         # マスタデータ管理
│   ├── bases/           # 拠点・組織管理
│   ├── seko/            # 施行（葬儀）管理
│   ├── orders/          # 注文管理
│   ├── hoyo/            # 法要管理
│   └── ...              # その他業務モジュール
└── foc-frontend/        # Angular SPAフロントエンド
    └── src/app/
        ├── pages/
        │   ├── company/  # 葬儀社向け画面
        │   ├── customer/ # 顧客向け画面
        │   └── family/   # 遺族向け画面
        └── ...
```

## 主要な技術スタック

### バックエンド
- **フレームワーク**: Django 3.1.14 + Django REST Framework
- **言語**: Python 3.8-3.9
- **データベース**: PostgreSQL
- **認証**: JWT（djangorestframework-simplejwt）
- **PDF生成**: WeasyPrint

### フロントエンド
- **フレームワーク**: Angular 10
- **UI**: Angular Material + Fomantic UI
- **言語**: TypeScript

## 主要な機能

1. **施行管理**: 葬儀の基本情報、故人・喪主情報、スケジュール管理
2. **注文管理**: 顧客からの注文受付、決済処理
3. **法要管理**: 法要の予約・管理、メール配信
4. **拠点管理**: 階層構造の組織管理
5. **返礼品管理**: 返礼品の管理・発注
6. **アフターフォロー**: 施行後のフォローアップ
7. **問い合わせ管理**: 顧客からの問い合わせ対応
8. **請求書管理**: 請求書生成・管理

## ユーザータイプ

1. **Company（葬儀社）**: 施行管理、注文管理、各種マスタ管理
2. **Customer（顧客）**: 商品注文、問い合わせ、法要申込
3. **Family（遺族）**: 施行情報閲覧、メモリアル機能

## 開発・運用

- **開発環境**: Poetry（Python）+ npm（Node.js）
- **本番環境**: Docker対応
- **監視**: Django Silk（開発時）
- **静的ファイル**: WhiteNoise

## 分析の目的

このドキュメントは以下の目的で作成されました：

1. **プロジェクト理解の促進**: 新規参加者がプロジェクト全体を理解できるよう
2. **アーキテクチャの可視化**: システムの構造と依存関係を明確化
3. **技術仕様の整理**: 開発・運用に必要な技術情報の集約
4. **保守性の向上**: 将来の機能追加・修正時の参考資料として

## 更新履歴

- 2025-06-25: 初版作成（プロジェクト分析実施）

## 注意事項

- このドキュメントは分析時点（2025-06-25）の情報に基づいています
- プロジェクトの変更に応じて定期的な更新が必要です
- 実装詳細については実際のソースコードを参照してください
