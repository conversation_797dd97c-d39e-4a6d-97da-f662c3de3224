# FOC（Funeral Online system Connect）プロジェクト分析

## プロジェクト概要

FOCは葬儀業界向けのオンラインシステムで、葬儀社、顧客、遺族の3つの主要なユーザーグループに対応したWebアプリケーションです。

### 技術スタック

**バックエンド（foc-backend）**
- **フレームワーク**: Django 3.1.14 + Django REST Framework
- **言語**: Python 3.8-3.9
- **データベース**: PostgreSQL（psycopg2-binary）
- **認証**: JWT（djangorestframework-simplejwt）
- **PDF生成**: WeasyPrint
- **その他**: django-cors-headers, django-filter, django-mptt

**フロントエンド（foc-frontend）**
- **フレームワーク**: Angular 10
- **UI**: Angular Material + Fomantic UI
- **言語**: TypeScript
- **その他**: jQuery, ng2-pdfjs-viewer, encoding-japanese

## アーキテクチャ構造

### 1. 主要ディレクトリ構造

```
foc/
├── foc-backend/          # Django REST APIバックエンド
│   ├── foc/             # Django設定・メインアプリ
│   ├── masters/         # マスタデータ管理
│   ├── bases/           # 拠点・組織管理
│   ├── staffs/          # スタッフ管理
│   ├── seko/            # 施行（葬儀）管理
│   ├── suppliers/       # 仕入先管理
│   ├── items/           # 商品・サービス管理
│   ├── orders/          # 注文管理
│   ├── henrei/          # 返礼品管理
│   ├── inquiry/         # 問い合わせ管理
│   ├── invoices/        # 請求書管理
│   ├── hoyo/            # 法要管理
│   ├── after_follow/    # アフターフォロー
│   ├── event_mails/     # イベントメール
│   ├── faqs/            # FAQ管理
│   └── advertises/      # 広告管理
└── foc-frontend/        # Angular SPAフロントエンド
    └── src/app/
        ├── pages/
        │   ├── company/  # 葬儀社向け画面
        │   ├── customer/ # 顧客向け画面
        │   └── family/   # 遺族向け画面
        ├── service/      # APIサービス
        ├── interfaces/   # TypeScript型定義
        └── components/   # 共通コンポーネント
```

### 2. バックエンドモジュール詳細

#### 2.1 コアモジュール

**masters（マスタ管理）**
- 日程項目、施行スタイル、郵便番号、和暦
- 不法サンプル、弔文代師、サービス、税率
- 関係性、返礼品パラメータ、香典パラメータ
- 法要スタイル、法要デフォルト、法要サンプル

**bases（拠点管理）**
- 階層構造の組織管理（MPTT使用）
- 会社→部門→ホール→その他ホールの階層
- 特商法情報、FOC手数料、SMS設定

**seko（施行管理）**
- 葬儀の基本情報管理
- 故人情報、喪主情報
- 施行スケジュール、アルバム、動画
- 問い合わせ・回答機能

#### 2.2 業務モジュール

**orders（注文管理）**
- 顧客からの注文受付
- 決済処理（Epsilon連携）
- 注文詳細管理

**henrei（返礼品管理）**
- 返礼品の管理・発注

**hoyo（法要管理）**
- 法要の予約・管理
- 法要メール配信
- 法要サンプル管理

**after_follow（アフターフォロー）**
- 施行後のフォローアップ
- 活動詳細管理

#### 2.3 サポートモジュール

**inquiry（問い合わせ）**
- 顧客からの問い合わせ管理

**invoices（請求書）**
- 請求書生成・管理

**event_mails（イベントメール）**
- 自動メール配信

**faqs（FAQ）**
- よくある質問管理

**advertises（広告）**
- 広告・宣伝管理

### 3. フロントエンド構造

#### 3.1 ユーザー別画面構成

**Company（葬儀社向け）**
- ログイン・トップ画面
- 施行管理（seko, seko-edit）
- 拠点管理（base）
- スタッフ管理（staff）
- 仕入先管理（supplier）
- 商品・サービス管理（item-service）
- 返礼品管理（henreihin, order-henreihin）
- 月次集計（monthly-summary, monthly-confirm）
- 売上集計（sales-summary）
- 請求書（invoice）
- 法要管理（hoyo-master, hoyo-sample, hoyo-mail-template）
- アフターフォロー（af-group, af-contact）
- FAQ管理（faq）
- 広告管理（advertise）

**Customer（顧客向け）**
- 不法申込（huho, huho-approve）
- 弔文注文（chobun1, chobun2）
- 供物注文（kumotsu）
- 香典注文（koden1, koden2）
- メッセージ（message）
- カート・注文（cart, order-entry, order-confirm, order-payment, order-complete）
- 問い合わせ（inquiry）
- 法要申込（hoyo）
- 各種規約（privacy-policy, tokushoho, userpolicy）

**Family（遺族向け）**
- 遺族認証（soke-approve, login）
- 施行情報確認（seko）
- 芳名帳（homei）
- 返礼品（henrei）
- 情報管理（info）
- パスワード変更（change-password）
- メモリアル（memorial）
- 画像共有（image-share）
- 法要（hoyo）
- アフターフォロー（seko-af）
- 問い合わせ（inquiry）

#### 3.2 共通機能

**Services**
- HTTP通信（http-client.service）
- セッション管理（session.service）
- キャッシュ（cache.service）
- ローダー（loader.service）
- 郵便番号検索（zipcode.service）

**Guards**
- 認証ガード（authF.guard, authS.guard）
- 管理者ガード（admin.guard）
- 注文ガード（order.guard）
- スタッフ専用ガード（staff.guard）

**Components**
- カレンダー（com-calendar）
- ドロップダウン（com-dropdown）
- 時間入力（com-time-input）

## データフロー

### 1. 認証フロー
```
フロントエンド → JWT認証 → バックエンドAPI → データベース
```

### 2. 施行管理フロー
```
葬儀社（Company） → 施行登録 → 遺族認証 → 遺族（Family）画面アクセス
```

### 3. 注文フロー
```
顧客（Customer） → 商品選択 → カート → 決済（Epsilon） → 注文確定
```

### 4. 法要管理フロー
```
顧客申込 → 葬儀社確認 → メール配信 → 法要実施
```

## モジュール間依存関係

### 主要な依存関係
- **seko** ← bases, masters, items, after_follow, hoyo
- **orders** ← bases, items, seko
- **henrei** ← seko, orders
- **hoyo** ← seko, masters
- **after_follow** ← seko
- **invoices** ← orders, seko

### データベース関係
- 階層構造: bases（MPTT）
- 外部キー: seko → bases, orders → seko
- 多対多: seko ↔ items, seko ↔ services

## 特徴的な機能

1. **階層組織管理**: django-mpttによる拠点の階層管理
2. **JWT認証**: 3つのユーザータイプ別認証
3. **PDF生成**: WeasyPrintによる帳票出力
4. **決済連携**: Epsilon決済システム連携
5. **メール配信**: 自動イベントメール機能
6. **画像・動画管理**: 施行関連メディア管理
7. **多言語対応**: Django国際化機能
8. **API設計**: RESTful API設計

## 開発・運用環境

- **開発**: Poetry（Python）+ npm（Node.js）
- **本番**: Docker対応
- **データベース**: PostgreSQL
- **監視**: Django Silk（開発時）
- **静的ファイル**: WhiteNoise
