# FOCプロジェクト モジュール詳細仕様

## バックエンドモジュール詳細

### 1. masters（マスタデータ管理）

**主要モデル**
- `Schedule`: 日程項目マスタ
- `SekoStyle`: 施行スタイルマスタ
- `ZipCode`: 郵便番号マスタ
- `Wareki`: 和暦マスタ
- `FuhoSampleMaster`: 不法サンプルマスタ
- `ChobunDaishiMaster`: 弔文代師マスタ
- `ChobunSample`: 弔文サンプルマスタ
- `Service`: サービスマスタ
- `Tax`: 税率マスタ
- `Relationship`: 関係性マスタ
- `HenreihinParameter`: 返礼品パラメータ
- `KodenParameter`: 香典パラメータ
- `HoyoStyle`: 法要スタイルマスタ
- `HoyoDefaultMaster`: 法要デフォルトマスタ
- `HoyoSampleMaster`: 法要サンプルマスタ
- `SokeMenu`: 遺族メニューマスタ

**主要API**
- `GET /masters/schedules/`: 日程項目一覧
- `GET /masters/seko_styles/`: 施行スタイル一覧
- `GET /masters/zipcode/`: 郵便番号検索
- `GET /masters/services/`: サービス一覧
- `GET /masters/taxes/`: 税率一覧

### 2. bases（拠点・組織管理）

**主要モデル**
- `Base`: 拠点情報（MPTT階層構造）
  - `OrgType`: ADMIN(0), COMPANY(1), DEPARTMENT(2), HALL(3), OTHER(9)
- `Tokusho`: 特定商取引法情報
- `FocFee`: FOC手数料設定
- `SmsAccount`: SMS設定

**主要API**
- `GET /bases/all/`: 全拠点階層一覧
- `GET /bases/<id>/`: 拠点詳細
- `PUT /bases/<id>/`: 拠点更新
- `GET /bases/<base_id>/tokusho/`: 特商法情報
- `GET /bases/<base_id>/focfee/`: FOC手数料

**特徴**
- django-mpttによる階層構造管理
- 会社→部門→ホール→その他ホールの4階層

### 3. seko（施行管理）

**主要モデル**
- `Seko`: 施行基本情報
- `Kojin`: 故人情報
- `Moshu`: 喪主情報
- `SekoSchedule`: 施行スケジュール
- `SekoItem`: 施行商品
- `SekoService`: 施行サービス
- `SekoAlbum`: 施行アルバム
- `SekoVideo`: 施行動画
- `SekoInquiry`: 施行問い合わせ
- `SekoAnswer`: 施行回答
- `SekoContact`: 施行連絡先
- `SekoShareImage`: 施行共有画像
- `HenreihinKakegami`: 返礼品掛紙

**主要API**
- `GET /seko/`: 施行一覧
- `POST /seko/`: 施行作成
- `GET /seko/<id>/`: 施行詳細
- `PUT /seko/<id>/`: 施行更新
- `GET /seko/<id>/kojin/`: 故人情報
- `GET /seko/<id>/moshu/`: 喪主情報
- `GET /seko/<id>/schedules/`: スケジュール一覧
- `GET /seko/<id>/albums/`: アルバム一覧

**認証機能**
- 喪主認証バックエンド（`auth_backends.py`）
- JWT認証による遺族アクセス制御

### 4. orders（注文管理）

**主要モデル**
- `Entry`: 注文エントリ
- `EntryDetail`: 注文詳細
- `PaymentResult`: 決済結果

**主要API**
- `GET /orders/`: 注文一覧
- `POST /orders/`: 注文作成
- `GET /orders/<id>/`: 注文詳細
- `POST /orders/<id>/payment/`: 決済処理

**決済連携**
- Epsilon決済システム連携（`epsilon.py`）
- クレジットカード決済対応

### 5. henrei（返礼品管理）

**機能**
- 返礼品の管理・発注
- 施行との連携

### 6. hoyo（法要管理）

**主要モデル**
- `Hoyo`: 法要情報
- `HoyoSeko`: 法要施行関連
- `HoyoSchedule`: 法要スケジュール
- `HoyoSample`: 法要サンプル
- `HoyoMail`: 法要メール
- `HoyoMailTemplate`: 法要メールテンプレート
- `HoyoMailTarget`: 法要メール対象

**主要API**
- `GET /hoyo/`: 法要一覧
- `POST /hoyo/`: 法要作成
- `GET /hoyo/<id>/`: 法要詳細
- `POST /hoyo/<id>/mail/`: メール送信

### 7. after_follow（アフターフォロー）

**主要モデル**
- `SekoAf`: 施行アフターフォロー
- `AfActivityDetail`: アフター活動詳細
- `AfWish`: アフター希望

### 8. その他モジュール

**suppliers（仕入先管理）**
- 仕入先情報管理

**items（商品・サービス管理）**
- 商品・サービス情報管理

**inquiry（問い合わせ管理）**
- 顧客問い合わせ管理

**invoices（請求書管理）**
- 請求書生成・管理

**event_mails（イベントメール）**
- 自動メール配信

**faqs（FAQ管理）**
- よくある質問管理

**advertises（広告管理）**
- 広告・宣伝管理

## フロントエンドページ詳細

### Company（葬儀社向け）

**認証・基本機能**
- `login`: ログイン画面
- `top`: トップ画面
- `frame`: 共通フレーム

**施行管理**
- `seko`: 施行一覧・管理
- `seko-edit`: 施行編集
- `seko-af`: 施行アフターフォロー

**マスタ管理**
- `base`: 拠点管理
- `staff`: スタッフ管理
- `supplier`: 仕入先管理
- `item-service`: 商品・サービス管理
- `fuho-sample`: 不法サンプル管理
- `chobun-daishi`: 弔文代師管理

**業務管理**
- `henreihin`: 返礼品管理
- `order-henreihin`: 返礼品注文管理
- `order-cancel`: 注文キャンセル
- `chobun`: 弔文管理
- `kumotsu`: 供物管理
- `koden`: 香典管理
- `koden-summary`: 香典集計
- `message`: メッセージ管理

**集計・帳票**
- `monthly-summary`: 月次集計
- `monthly-confirm`: 月次確認
- `sales-summary`: 売上集計
- `invoice`: 請求書

**法要管理**
- `hoyo-master`: 法要マスタ
- `hoyo-sample`: 法要サンプル
- `hoyo-mail-template`: 法要メールテンプレート
- `hoyo-filter-ki`: 法要フィルタ（忌）
- `hoyo-filter-bon`: 法要フィルタ（盆）
- `hoyo-guidance`: 法要案内

**その他**
- `af-group`: アフターフォローグループ
- `af-contact`: アフターフォロー連絡
- `event-filter`: イベントフィルタ
- `event-guidance`: イベント案内
- `faq`: FAQ管理
- `advertise`: 広告管理
- `soke-menu`: 遺族メニュー

### Customer（顧客向け）

**基本機能**
- `frame`: 共通フレーム
- `sitepolicy`: サイトポリシー
- `privacy-policy`: プライバシーポリシー
- `tokushoho`: 特定商取引法
- `userpolicy`: 利用規約

**申込・注文**
- `huho`: 不法申込
- `huho-approve`: 不法承認
- `chobun1`: 弔文注文1
- `chobun2`: 弔文注文2
- `kumotsu`: 供物注文
- `koden1`: 香典注文1
- `koden2`: 香典注文2
- `message`: メッセージ
- `cart`: カート
- `order-entry`: 注文入力
- `order-confirm`: 注文確認
- `order-payment`: 決済
- `order-complete`: 注文完了
- `receipt`: 領収書

**その他**
- `inquiry`: 問い合わせ
- `hoyo`: 法要申込
- `howto-share`: 共有方法

### Family（遺族向け）

**認証**
- `soke-approve`: 遺族承認
- `login`: ログイン
- `change-password`: パスワード変更

**施行関連**
- `seko`: 施行情報
- `homei`: 芳名帳
- `henrei`: 返礼品
- `info`: 情報管理
- `seko-af`: 施行アフターフォロー

**メモリアル**
- `memorial`: メモリアル
- `image-share`: 画像共有

**その他**
- `hoyo`: 法要
- `inquiry`: 問い合わせ
- `faq`: FAQ
- `soke-userpolicy`: 遺族利用規約
- `soke-manual`: 遺族マニュアル（会社別）

## 共通機能・コンポーネント

### Services
- `http-client.service`: HTTP通信
- `session.service`: セッション管理
- `cache.service`: キャッシュ管理
- `loader.service`: ローディング管理
- `zipcode.service`: 郵便番号検索
- `staff.service`: スタッフ管理
- `base.service`: 基本サービス
- `router.service`: ルーティング

### Guards
- `authF.guard`: 遺族認証ガード
- `authS.guard`: スタッフ認証ガード
- `admin.guard`: 管理者ガード
- `order.guard`: 注文ガード
- `staff.guard`: スタッフ専用ガード

### Components
- `com-calendar`: カレンダーコンポーネント
- `com-dropdown`: ドロップダウンコンポーネント
- `com-time-input`: 時間入力コンポーネント

### Interfaces
- TypeScript型定義
- API レスポンス・リクエスト型
- ビジネスロジック型定義
