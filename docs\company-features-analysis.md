# Company画面機能詳細分析

## 権限レベル別機能一覧

### 1. システム管理者専用機能（ADMIN）

#### 拠点管理（base）
- **機能**: 階層構造の組織管理
- **権限**: AdminGuard必須
- **詳細**:
  - 葬儀社（COMPANY）の作成・編集・削除
  - 部門（DEPARTMENT）の作成・編集・削除
  - 式場（HALL）の作成・編集・削除
  - その他ホール（OTHER）の作成・編集・削除
  - 特定商取引法情報の管理
  - FOC手数料設定
  - SMS設定

#### 仕入先管理（supplier）
- **機能**: 仕入先情報の管理
- **権限**: AdminGuard必須
- **詳細**:
  - 仕入先の登録・編集・削除
  - 仕入先情報の検索・一覧表示
  - 仕入先との取引条件管理

#### スタッフ管理（staff）
- **機能**: システム全体のスタッフ管理
- **権限**: AdminGuard必須
- **詳細**:
  - スタッフの登録・編集・削除
  - 権限レベルの設定
  - 所属拠点の設定
  - ログイン情報の管理

#### 不法サンプル管理（fuho-sample）
- **機能**: 不法申込書のサンプル管理
- **権限**: AdminGuard必須
- **詳細**:
  - 不法サンプルの登録・編集・削除
  - サンプルファイルのアップロード
  - 会社別サンプル設定

#### 弔文台紙管理（chobun-daishi）
- **機能**: 弔文の台紙デザイン管理
- **権限**: AdminGuard必須
- **詳細**:
  - 台紙デザインの登録・編集・削除
  - 台紙画像のアップロード
  - 会社別台紙設定

#### 商品サービス管理（item-service）
- **機能**: システム全体の商品・サービス管理
- **権限**: AdminGuard必須
- **詳細**:
  - 商品・サービスの登録・編集・削除
  - 価格設定・税率設定
  - 商品画像の管理
  - 一括インポート・エクスポート

#### 広告管理（advertise）
- **機能**: システム内広告の管理
- **権限**: AdminGuard必須
- **詳細**:
  - 広告の作成・編集・削除
  - 広告画像・動画のアップロード
  - 表示期間・対象の設定

#### FAQ管理（faq）
- **機能**: よくある質問の管理
- **権限**: AdminGuard必須
- **詳細**:
  - FAQ項目の作成・編集・削除
  - カテゴリ分類
  - 表示順序の設定

### 2. 葬儀社・システム共通機能

#### 施行管理（seko, seko-edit）
- **機能**: 葬儀施行の総合管理
- **権限**: AuthFGuard（スタッフ認証）
- **詳細**:
  - 施行基本情報の登録・編集
  - 故人情報・喪主情報の管理
  - 施行スケジュールの作成・管理
  - 施行商品・サービスの選択
  - アルバム・動画の管理
  - 共有画像の管理
  - 問い合わせ・回答の管理
  - 不法書PDF生成
  - 喪主認証設定
  - QRコード生成

#### 返礼品管理（henreihin, order-henreihin）
- **機能**: 返礼品の注文・管理
- **権限**: AuthFGuard
- **詳細**:
  - 返礼品の検索・一覧表示
  - 返礼品注文の作成・編集
  - 注文ステータスの管理
  - 返礼品PDF・FAX送信
  - 香典・供物との連携

#### 弔文受付（chobun）
- **機能**: 弔文注文の管理
- **権限**: AuthFGuard
- **詳細**:
  - 弔文注文の検索・一覧表示
  - 注文内容の確認・編集
  - 弔文PDF生成
  - 注文ステータス管理

#### 供物管理（kumotsu）
- **機能**: 供花・供物注文の管理
- **権限**: AuthFGuard
- **詳細**:
  - 供物注文の検索・一覧表示
  - 注文内容の確認・編集
  - 供物PDF・FAX送信
  - 注文ステータス管理

#### 香典管理（koden, koden-summary）
- **機能**: 香典の管理・集計
- **権限**: AuthFGuard
- **詳細**:
  - 香典一覧の表示
  - 香典集計機能
  - 香典帳の出力

#### メッセージ管理（message）
- **機能**: メッセージの管理
- **権限**: AuthFGuard
- **詳細**:
  - メッセージ一覧の表示
  - メッセージ内容の確認
  - メッセージPDF出力

#### 注文キャンセル（order-cancel）
- **機能**: 各種注文のキャンセル処理
- **権限**: AuthFGuard
- **詳細**:
  - 注文キャンセルの実行
  - キャンセル理由の記録
  - 返金処理の管理

#### 月次集計（monthly-summary, monthly-confirm）
- **機能**: 月次売上・業績の集計
- **権限**: AuthFGuard
- **詳細**:
  - 月次売上集計
  - 月次確定処理
  - 集計レポート出力

#### 売上集計（sales-summary）
- **機能**: 売上データの集計・分析
- **権限**: AuthFGuard
- **詳細**:
  - 期間別売上集計
  - 商品別売上分析
  - 拠点別売上比較

#### 請求書（invoice）
- **機能**: 請求書の生成・管理
- **権限**: AuthFGuard
- **詳細**:
  - 請求書の作成・編集
  - 請求書PDF生成
  - 請求ステータス管理

#### 法要管理（hoyo-master, hoyo-sample, hoyo-mail-template）
- **機能**: 法要の総合管理
- **権限**: AuthFGuard
- **詳細**:
  - 法要マスタの管理
  - 法要サンプルの作成・編集
  - 法要メールテンプレートの管理
  - 法要案内メールの送信
  - 法要PDF生成

#### アフターフォロー（af-group, af-contact, seko-af）
- **機能**: 施行後のフォローアップ
- **権限**: AuthFGuard
- **詳細**:
  - アフターフォローグループの管理
  - 連絡方法の設定
  - 施行別アフターフォロー管理
  - 活動記録の管理

#### イベント管理（event-guidance, event-filter）
- **機能**: イベント・案内の管理
- **権限**: AuthFGuard
- **詳細**:
  - イベント案内の作成・送信
  - イベントフィルタの設定
  - 対象者の絞り込み

#### 法要フィルタ（hoyo-filter-ki, hoyo-filter-bon）
- **機能**: 法要対象者のフィルタリング
- **権限**: AuthFGuard
- **詳細**:
  - 忌日法要フィルタ
  - お盆法要フィルタ
  - 対象者の自動抽出

#### 法要案内（hoyo-guidance）
- **機能**: 法要案内の送信
- **権限**: AuthFGuard
- **詳細**:
  - 法要案内の一括送信
  - 個別案内の送信
  - 送信履歴の管理

#### 遺族メニュー（soke-menu）
- **機能**: 遺族向けメニューの設定
- **権限**: AuthFGuard
- **詳細**:
  - 遺族サイトメニューの設定
  - 表示項目の制御
  - 会社別カスタマイズ

### 3. 権限制御の実装

#### ルーティングレベル
```typescript
// AdminGuard必須の機能
{ path: 'foc/base', canActivate: [AuthFGuard, AdminGuard] }
{ path: 'foc/supplier', canActivate: [AuthFGuard, AdminGuard] }
{ path: 'foc/staff', canActivate: [AuthFGuard, AdminGuard] }

// スタッフ認証のみ必須
{ path: 'foc/seko', canActivate: [AuthFGuard] }
{ path: 'foc/henreihin', canActivate: [AuthFGuard] }
```

#### コンポーネントレベル
```typescript
// 拠点タイプによる表示制御
*ngIf="login_company.base_type === Const.BASE_TYPE_ADMIN"

// 権限による機能制限
if ([CommonConstants.BASE_TYPE_ADMIN, CommonConstants.BASE_TYPE_COMPANY]
    .includes(login_info.staff.base.base_type))
```

### 4. データアクセス制御

#### 階層アクセス制御
- **ADMIN**: 全拠点のデータにアクセス可能
- **COMPANY**: 自社および下位拠点のデータにアクセス可能
- **DEPARTMENT**: 自部門および下位拠点のデータにアクセス可能
- **HALL**: 自拠点のデータのみアクセス可能

#### API レベル制御
```python
def get_queryset(self):
    user = self.request.user
    if user.staff.base.base_type == BaseType.ADMIN:
        return Seko.objects.all()
    else:
        accessible_bases = user.staff.base.get_descendants(include_self=True)
        return Seko.objects.filter(base__in=accessible_bases)
```
