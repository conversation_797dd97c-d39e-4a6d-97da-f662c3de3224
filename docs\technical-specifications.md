# FOCプロジェクト 技術仕様書

## システム要件

### 動作環境
- **Python**: 3.8以上、3.10未満
- **Node.js**: v12.18.3
- **データベース**: PostgreSQL
- **Webサーバー**: Apache/Nginx + Gunicorn/uvicorn

### 依存関係

#### バックエンド（Python/Django）
```toml
[tool.poetry.dependencies]
python = ">=3.8,<3.10"
Django = "^3.1.14"
djangorestframework = "^3.12.2"
djangorestframework-simplejwt = "^4.6.0"
django-cors-headers = "^3.6.0"
django-filter = "^2.4.0"
django-mptt = "^0.11.0"
psycopg2-binary = "^2.8.6"
WeasyPrint = "^52.2"
gunicorn = "^20.0.4"
uvicorn = "^0.13.3"
```

#### フロントエンド（Angular/TypeScript）
```json
{
  "dependencies": {
    "@angular/core": "~10.0.8",
    "@angular/material": "^10.1.2",
    "fomantic-ui-css": "^2.8.7",
    "jquery": "^3.5.1",
    "ng2-pdfjs-viewer": "^6.0.1",
    "encoding-japanese": "^1.0.30"
  }
}
```

## アーキテクチャ設計

### 1. レイヤー構造

```
┌─────────────────────────────────────┐
│         Presentation Layer          │
│    (Angular SPA + Material UI)     │
├─────────────────────────────────────┤
│           API Layer                 │
│     (Django REST Framework)        │
├─────────────────────────────────────┤
│         Business Logic              │
│    (Django Models + Serializers)   │
├─────────────────────────────────────┤
│         Data Access Layer          │
│      (Django ORM + PostgreSQL)     │
└─────────────────────────────────────┘
```

### 2. モジュール設計原則

**単一責任の原則**
- 各Djangoアプリは単一の業務領域を担当
- 例：`seko`（施行管理）、`orders`（注文管理）、`hoyo`（法要管理）

**依存関係の逆転**
- 上位モジュールは下位モジュールに依存しない
- 抽象に依存し、具象に依存しない

**開放閉鎖の原則**
- 拡張に対して開放、修正に対して閉鎖
- カスタマイズ可能な設計

## データベース設計

### 1. 主要テーブル設計

#### bases（拠点管理）
```sql
CREATE TABLE bases_base (
    id SERIAL PRIMARY KEY,
    base_type INTEGER NOT NULL,
    parent_id INTEGER REFERENCES bases_base(id),
    company_code VARCHAR(20),
    base_name TEXT NOT NULL,
    zip_code VARCHAR(7) NOT NULL,
    prefecture TEXT NOT NULL,
    address_1 TEXT NOT NULL,
    address_2 TEXT,
    address_3 TEXT,
    tel VARCHAR(20),
    fax VARCHAR(20),
    lft INTEGER NOT NULL,
    rght INTEGER NOT NULL,
    tree_id INTEGER NOT NULL,
    level INTEGER NOT NULL,
    del_flg BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### seko（施行管理）
```sql
CREATE TABLE seko_seko (
    id SERIAL PRIMARY KEY,
    base_id INTEGER NOT NULL REFERENCES bases_base(id),
    seko_no VARCHAR(50) UNIQUE NOT NULL,
    seko_date DATE,
    seko_style_id INTEGER REFERENCES masters_sekostyle(id),
    seko_place TEXT,
    del_flg BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE seko_kojin (
    id SERIAL PRIMARY KEY,
    seko_id INTEGER NOT NULL REFERENCES seko_seko(id),
    kojin_name VARCHAR(100) NOT NULL,
    kojin_kana VARCHAR(100),
    birth_date DATE,
    death_date DATE,
    age INTEGER,
    gender INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE seko_moshu (
    id SERIAL PRIMARY KEY,
    seko_id INTEGER NOT NULL REFERENCES seko_seko(id),
    moshu_name VARCHAR(100) NOT NULL,
    moshu_kana VARCHAR(100),
    tel VARCHAR(20),
    email VARCHAR(254),
    password VARCHAR(128),
    last_login TIMESTAMP,
    agree_ts TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. インデックス設計

```sql
-- 検索性能向上のためのインデックス
CREATE INDEX idx_seko_base_id ON seko_seko(base_id);
CREATE INDEX idx_seko_seko_no ON seko_seko(seko_no);
CREATE INDEX idx_seko_date ON seko_seko(seko_date);
CREATE INDEX idx_kojin_seko_id ON seko_kojin(seko_id);
CREATE INDEX idx_moshu_seko_id ON seko_moshu(seko_id);
CREATE INDEX idx_moshu_email ON seko_moshu(email);

-- 階層構造検索用インデックス（MPTT）
CREATE INDEX idx_base_tree_id ON bases_base(tree_id);
CREATE INDEX idx_base_lft_rght ON bases_base(lft, rght);
```

## API設計

### 1. RESTful API設計原則

**リソース指向**
```
GET    /api/seko/           # 施行一覧取得
POST   /api/seko/           # 施行作成
GET    /api/seko/{id}/      # 施行詳細取得
PUT    /api/seko/{id}/      # 施行更新
DELETE /api/seko/{id}/      # 施行削除
```

**ネストしたリソース**
```
GET    /api/seko/{id}/kojin/     # 故人情報取得
PUT    /api/seko/{id}/kojin/     # 故人情報更新
GET    /api/seko/{id}/schedules/ # スケジュール一覧
POST   /api/seko/{id}/schedules/ # スケジュール作成
```

### 2. レスポンス形式

**成功レスポンス**
```json
{
  "id": 1,
  "seko_no": "2024-001",
  "seko_date": "2024-01-15",
  "base": {
    "id": 1,
    "base_name": "本社"
  },
  "kojin": {
    "kojin_name": "山田太郎",
    "death_date": "2024-01-10"
  },
  "created_at": "2024-01-01T10:00:00Z",
  "updated_at": "2024-01-01T10:00:00Z"
}
```

**エラーレスポンス**
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "入力データに誤りがあります",
    "details": {
      "seko_date": ["この項目は必須です"],
      "kojin_name": ["100文字以内で入力してください"]
    }
  }
}
```

### 3. 認証・認可

**JWT認証**
```python
# settings.py
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'ALGORITHM': 'RS256',
    'SIGNING_KEY': private_key,
    'VERIFYING_KEY': public_key,
}
```

**権限制御**
```python
# views.py
class SekoViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            return Seko.objects.filter(base__in=user.accessible_bases)
        else:
            # 遺族の場合は自分の施行のみ
            return Seko.objects.filter(moshu__email=user.email)
```

## フロントエンド設計

### 1. コンポーネント設計

**階層構造**
```
AppComponent
├── CompanyFrameComponent
│   ├── LoginComponent
│   ├── TopComponent
│   ├── SekoComponent
│   │   └── SekoEditComponent
│   └── ...
├── CustomerFrameComponent
│   ├── HuhoComponent
│   ├── OrderEntryComponent
│   └── ...
└── FamilyFrameComponent
    ├── SokeLoginComponent
    ├── SokeSekoComponent
    └── ...
```

**共通コンポーネント**
```typescript
// com-calendar.component.ts
@Component({
  selector: 'app-com-calendar',
  template: `
    <div class="calendar-container">
      <!-- カレンダー実装 -->
    </div>
  `
})
export class ComCalendarComponent {
  @Input() selectedDate: Date;
  @Output() dateChange = new EventEmitter<Date>();
}
```

### 2. サービス設計

**HTTP通信サービス**
```typescript
@Injectable()
export class HttpClientService {
  constructor(private http: HttpClient) {}
  
  getSekoList(params?: any): Observable<SekoListResponse> {
    return this.http.get<SekoListResponse>('/api/seko/', { params });
  }
  
  getSekoDetail(id: number): Observable<SekoDetailResponse> {
    return this.http.get<SekoDetailResponse>(`/api/seko/${id}/`);
  }
  
  updateSeko(id: number, data: SekoUpdateRequest): Observable<SekoDetailResponse> {
    return this.http.put<SekoDetailResponse>(`/api/seko/${id}/`, data);
  }
}
```

**状態管理**
```typescript
@Injectable()
export class SessionService {
  private currentUser$ = new BehaviorSubject<User | null>(null);
  
  getCurrentUser(): Observable<User | null> {
    return this.currentUser$.asObservable();
  }
  
  setCurrentUser(user: User): void {
    this.currentUser$.next(user);
    localStorage.setItem('currentUser', JSON.stringify(user));
  }
}
```

### 3. ルーティング設計

```typescript
const routes: Routes = [
  {
    path: 'company',
    component: CompanyFrameComponent,
    canActivate: [AuthSGuard],
    children: [
      { path: 'login', component: LoginComponent },
      { path: 'top', component: TopComponent },
      { path: 'seko', component: SekoComponent },
      { path: 'seko/:id/edit', component: SekoEditComponent },
    ]
  },
  {
    path: 'customer',
    component: CustomerFrameComponent,
    children: [
      { path: 'huho', component: HuhoComponent },
      { path: 'order', component: OrderEntryComponent },
    ]
  },
  {
    path: 'family',
    component: FamilyFrameComponent,
    canActivate: [AuthFGuard],
    children: [
      { path: 'login', component: SokeLoginComponent },
      { path: 'seko', component: SokeSekoComponent },
    ]
  }
];
```

## セキュリティ設計

### 1. 認証セキュリティ

**パスワードハッシュ化**
```python
# Django標準のPBKDF2使用
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {'min_length': 8,}
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]
```

**JWT セキュリティ**
```python
# RSA256による署名
SIMPLE_JWT = {
    'ALGORITHM': 'RS256',
    'SIGNING_KEY': private_key,
    'VERIFYING_KEY': public_key,
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
}
```

### 2. データ保護

**CORS設定**
```python
CORS_ALLOWED_ORIGINS = [
    "https://example.com",
    "https://sub.example.com",
]

CORS_ALLOW_CREDENTIALS = True
```

**HTTPS強制**
```python
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
```

### 3. 入力検証

**Serializer検証**
```python
class SekoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Seko
        fields = '__all__'
    
    def validate_seko_date(self, value):
        if value > timezone.now().date():
            raise serializers.ValidationError("未来の日付は設定できません")
        return value
```

**フロントエンド検証**
```typescript
this.sekoForm = this.fb.group({
  seko_date: ['', [Validators.required]],
  kojin_name: ['', [Validators.required, Validators.maxLength(100)]],
  moshu_email: ['', [Validators.required, Validators.email]]
});
```

## パフォーマンス最適化

### 1. データベース最適化

**クエリ最適化**
```python
# N+1問題の回避
seko_list = Seko.objects.select_related('base', 'seko_style')\
                       .prefetch_related('kojin', 'moshu')\
                       .filter(del_flg=False)
```

**ページネーション**
```python
class SekoListView(generics.ListAPIView):
    pagination_class = PageNumberPagination
    page_size = 20
```

### 2. フロントエンド最適化

**遅延読み込み**
```typescript
const routes: Routes = [
  {
    path: 'company',
    loadChildren: () => import('./pages/company/company.module').then(m => m.CompanyModule)
  }
];
```

**キャッシュ戦略**
```typescript
@Injectable()
export class CacheService {
  private cache = new Map<string, any>();
  
  get(key: string): any {
    return this.cache.get(key);
  }
  
  set(key: string, value: any, ttl: number = 300000): void {
    this.cache.set(key, value);
    setTimeout(() => this.cache.delete(key), ttl);
  }
}
```

## 運用・監視

### 1. ログ設定

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'django.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### 2. 監視・メトリクス

**Django Silk（開発環境）**
```python
if ENABLE_SILK:
    INSTALLED_APPS.append('silk')
    MIDDLEWARE.insert(0, 'silk.middleware.SilkyMiddleware')
```

**ヘルスチェック**
```python
# urls.py
path('health/', views.health_check, name='health_check'),

# views.py
def health_check(request):
    return JsonResponse({'status': 'ok', 'timestamp': timezone.now()})
```
